package com.geeksec.common.constants;

/**
 * 通用常量类
 * 
 * 整合了原有的两个CommonConstants类，提供NTA 3.0项目中的通用常量定义
 * 包括：字符编码、时区、分页、HTTP状态码、正则表达式、消息常量等
 *
 * <AUTHOR>
 * @since 3.0.0
 */
public final class CommonConstants {

    private CommonConstants() {
        // 工具类，禁止实例化
    }

    /**
     * 字符编码相关常量
     */
    public static final class Charset {
        /** 默认字符编码 */
        public static final String DEFAULT_CHARSET = "UTF-8";
        /** ISO-8859-1编码 */
        public static final String ISO_8859_1 = "ISO-8859-1";
        /** GBK编码 */
        public static final String GBK = "GBK";
        /** GB2312编码 */
        public static final String GB2312 = "GB2312";

        private Charset() {}
    }

    /**
     * 时区相关常量
     */
    public static final class TimeZone {
        /** 默认时区 */
        public static final String DEFAULT_TIMEZONE = "Asia/Shanghai";
        /** UTC时区 */
        public static final String UTC = "UTC";
        /** GMT时区 */
        public static final String GMT = "GMT";

        private TimeZone() {}
    }

    /**
     * 时间格式相关常量
     */
    public static final class DateFormat {
        /** 标准日期时间格式 */
        public static final String DATETIME = "yyyy-MM-dd HH:mm:ss";
        /** 日期格式 */
        public static final String DATE = "yyyy-MM-dd";
        /** 时间格式 */
        public static final String TIME = "HH:mm:ss";
        /** 时间戳格式 */
        public static final String TIMESTAMP = "yyyy-MM-dd HH:mm:ss.SSS";
        /** ISO 8601格式 */
        public static final String ISO_8601 = "yyyy-MM-dd'T'HH:mm:ss.SSSZ";

        private DateFormat() {}
    }

    /**
     * 分页相关常量
     */
    public static final class Page {
        /** 默认页码 */
        public static final int DEFAULT_PAGE = 1;
        /** 默认分页大小 */
        public static final int DEFAULT_SIZE = 20;
        /** 最大分页大小 */
        public static final int MAX_SIZE = 1000;
        /** 最小分页大小 */
        public static final int MIN_SIZE = 1;

        private Page() {}
    }

    /**
     * 网络相关常量
     */
    public static final class Network {
        /** 本地回环地址 */
        public static final String LOCALHOST = "127.0.0.1";
        /** IPv6本地回环地址 */
        public static final String LOCALHOST_IPV6 = "0:0:0:0:0:0:0:1";
        /** 默认端口 */
        public static final int DEFAULT_PORT = 8080;
        /** HTTP默认端口 */
        public static final int HTTP_PORT = 80;
        /** HTTPS默认端口 */
        public static final int HTTPS_PORT = 443;

        private Network() {}
    }

    /**
     * 正则表达式相关常量
     */
    public static final class Regex {
        /** 邮箱正则 */
        public static final String EMAIL = "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$";
        /** 手机号正则 */
        public static final String MOBILE = "^1[3-9]\\d{9}$";
        /** IPv4地址正则 */
        public static final String IPV4 = "^((25[0-5]|2[0-4]\\d|[01]?\\d\\d?)\\.){3}(25[0-5]|2[0-4]\\d|[01]?\\d\\d?)$";
        /** 域名正则 */
        public static final String DOMAIN = "^[a-zA-Z0-9]([a-zA-Z0-9\\-]{0,61}[a-zA-Z0-9])?\\.[a-zA-Z]{2,}$";
        /** URL正则 */
        public static final String URL = "^(https?|ftp)://[^\\s/$.?#].[^\\s]*$";
        /** 身份证号正则 */
        public static final String ID_CARD = "^[1-9]\\d{5}(18|19|20)\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$";

        private Regex() {}
    }

    /**
     * 消息相关常量
     */
    public static final class Message {
        /** 操作成功 */
        public static final String SUCCESS = "操作成功";
        /** 操作失败 */
        public static final String FAILED = "操作失败";
        /** 参数错误 */
        public static final String PARAM_ERROR = "参数错误";
        /** 数据不存在 */
        public static final String DATA_NOT_FOUND = "数据不存在";
        /** 权限不足 */
        public static final String PERMISSION_DENIED = "权限不足";
        /** 系统异常 */
        public static final String SYSTEM_ERROR = "系统异常";

        private Message() {}
    }

    /**
     * 缓存相关常量
     */
    public static final class Cache {
        /** 默认缓存过期时间（秒） */
        public static final long DEFAULT_EXPIRE = 3600L;
        /** 短期缓存过期时间（5分钟） */
        public static final long SHORT_EXPIRE = 300L;
        /** 长期缓存过期时间（24小时） */
        public static final long LONG_EXPIRE = 86400L;
        /** 永不过期 */
        public static final long NEVER_EXPIRE = -1L;

        private Cache() {}
    }

    /**
     * 文件相关常量
     */
    public static final class File {
        /** 默认文件大小限制（10MB） */
        public static final long DEFAULT_MAX_SIZE = 10 * 1024 * 1024L;
        /** 图片文件扩展名 */
        public static final String[] IMAGE_EXTENSIONS = {"jpg", "jpeg", "png", "gif", "bmp", "webp"};
        /** 文档文件扩展名 */
        public static final String[] DOCUMENT_EXTENSIONS = {"pdf", "doc", "docx", "xls", "xlsx", "ppt", "pptx", "txt"};

        private File() {}
    }

    /**
     * 系统相关常量
     */
    public static final class System {
        /** 系统名称 */
        public static final String SYSTEM_NAME = "NTA 3.0";
        /** 系统版本 */
        public static final String SYSTEM_VERSION = "3.0.0";
        /** 默认管理员用户名 */
        public static final String DEFAULT_ADMIN = "admin";

        private System() {}
    }
}

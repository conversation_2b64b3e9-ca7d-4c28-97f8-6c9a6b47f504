package com.geeksec.common.database.doris;

import lombok.extern.slf4j.Slf4j;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.util.Properties;

/**
 * Doris连接管理器
 * 提供Doris连接和操作功能
 *
 * <AUTHOR>
 */
@Slf4j
public class DorisConnectionManager {

    /**
     * 配置属性
     */
    private static Properties config = new Properties();

    /**
     * 私有构造函数，防止实例化
     */
    private DorisConnectionManager() {
        // 工具类，不允许实例化
    }

    /**
     * 初始化配置
     *
     * @param properties 配置属性
     */
    public static void initConfig(Properties properties) {
        config = properties;
    }

    /**
     * 获取数据库连接
     *
     * @return Doris数据库连接
     * @throws SQLException 连接异常
     */
    public static Connection getConnection() throws SQLException {
        String host = config.getProperty("doris.host", "localhost");
        String port = config.getProperty("doris.query.port", "9030");
        String database = config.getProperty("doris.database", "nta");
        String username = config.getProperty("doris.username", "root");
        String password = config.getProperty("doris.password", "");

        String url = String.format("jdbc:mysql://%s:%s/%s", host, port, database);
        
        Properties props = new Properties();
        props.setProperty("user", username);
        props.setProperty("password", password);
        props.setProperty("useSSL", "false");
        props.setProperty("allowPublicKeyRetrieval", "true");

        return DriverManager.getConnection(url, props);
    }

    /**
     * 测试连接
     *
     * @return 连接是否成功
     */
    public static boolean testConnection() {
        try (Connection conn = getConnection()) {
            log.info("Doris连接测试成功");
            return conn != null && !conn.isClosed();
        } catch (Exception e) {
            log.error("Doris连接测试失败", e);
            return false;
        }
    }


}

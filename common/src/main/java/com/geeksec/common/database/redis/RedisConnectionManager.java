package com.geeksec.common.database.redis;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;
import redis.clients.jedis.JedisPoolConfig;

import java.util.Map;
import java.util.Properties;

/**
 * Redis连接管理器
 * 提供Redis连接池和常用操作方法
 *
 * <AUTHOR>
 */
@Slf4j
public class RedisConnectionManager {

    /**
     * Redis连接池
     */
    private static JedisPool jedisPool = null;

    /**
     * 配置属性
     */
    private static Properties config = new Properties();

    /**
     * 初始化配置
     *
     * @param properties 配置属性
     */
    public static void initConfig(Properties properties) {
        config = properties;
    }

    /**
     * 初始化Redis连接池
     *
     * @return Redis连接池
     */
    public static synchronized JedisPool initJedisPool() {
        if (jedisPool != null && !jedisPool.isClosed()) {
            return jedisPool;
        }

        try {
            String host = config.getProperty("redis.host", "localhost");
            int port = Integer.parseInt(config.getProperty("redis.port", "6379"));
            String password = config.getProperty("redis.password", "");
            int timeout = Integer.parseInt(config.getProperty("redis.timeout", "10000"));

            JedisPoolConfig poolConfig = new JedisPoolConfig();
            poolConfig.setMaxTotal(Integer.parseInt(config.getProperty("redis.pool.max-total", "100")));
            poolConfig.setMaxIdle(Integer.parseInt(config.getProperty("redis.pool.max-idle", "50")));
            poolConfig.setMinIdle(Integer.parseInt(config.getProperty("redis.pool.min-idle", "10")));
            poolConfig.setMaxWaitMillis(Long.parseLong(config.getProperty("redis.pool.max-wait", "10000")));
            poolConfig.setTestOnBorrow(true);
            poolConfig.setTestOnReturn(true);

            if (StringUtils.isNotEmpty(password)) {
                jedisPool = new JedisPool(poolConfig, host, port, timeout, password);
            } else {
                jedisPool = new JedisPool(poolConfig, host, port, timeout);
            }
            log.info("Redis连接池初始化成功");
        } catch (Exception e) {
            log.error("Redis连接池初始化失败", e);
        }
        return jedisPool;
    }

    /**
     * 获取Jedis实例
     *
     * @return Jedis实例
     */
    public static synchronized Jedis getJedis() {
        if (jedisPool == null || jedisPool.isClosed()) {
            jedisPool = initJedisPool();
        }
        return jedisPool.getResource();
    }

    /**
     * 关闭连接池
     */
    public static synchronized void closePool() {
        if (jedisPool != null && !jedisPool.isClosed()) {
            jedisPool.close();
            log.info("Redis连接池已关闭");
        }
    }

    /**
     * 设置键值对
     *
     * @param key   键
     * @param value 值
     * @return 是否成功
     */
    public static boolean set(String key, String value) {
        try (Jedis jedis = getJedis()) {
            String result = jedis.set(key, value);
            return "OK".equals(result);
        } catch (Exception e) {
            log.error("Redis设置键值对失败: key={}, value={}", key, value, e);
            return false;
        }
    }

    /**
     * 设置键值对并指定过期时间
     *
     * @param key     键
     * @param value   值
     * @param seconds 过期时间（秒）
     * @return 是否成功
     */
    public static boolean setex(String key, String value, int seconds) {
        try (Jedis jedis = getJedis()) {
            String result = jedis.setex(key, seconds, value);
            return "OK".equals(result);
        } catch (Exception e) {
            log.error("Redis设置键值对（带过期时间）失败: key={}, value={}, seconds={}", key, value, seconds, e);
            return false;
        }
    }

    /**
     * 获取值
     *
     * @param key 键
     * @return 值
     */
    public static String get(String key) {
        try (Jedis jedis = getJedis()) {
            return jedis.get(key);
        } catch (Exception e) {
            log.error("Redis获取值失败: key={}", key, e);
            return null;
        }
    }

    /**
     * 删除键
     *
     * @param key 键
     * @return 删除的键数量
     */
    public static Long del(String key) {
        try (Jedis jedis = getJedis()) {
            return jedis.del(key);
        } catch (Exception e) {
            log.error("Redis删除键失败: key={}", key, e);
            return 0L;
        }
    }

    /**
     * 检查键是否存在
     *
     * @param key 键
     * @return 是否存在
     */
    public static boolean exists(String key) {
        try (Jedis jedis = getJedis()) {
            return jedis.exists(key);
        } catch (Exception e) {
            log.error("Redis检查键是否存在失败: key={}", key, e);
            return false;
        }
    }

    /**
     * 设置键的过期时间
     *
     * @param key     键
     * @param seconds 过期时间（秒）
     * @return 是否成功
     */
    public static boolean expire(String key, int seconds) {
        try (Jedis jedis = getJedis()) {
            return jedis.expire(key, seconds) == 1;
        } catch (Exception e) {
            log.error("Redis设置键过期时间失败: key={}, seconds={}", key, seconds, e);
            return false;
        }
    }

    /**
     * 获取键的剩余过期时间
     *
     * @param key 键
     * @return 剩余过期时间（秒），-1表示永不过期，-2表示键不存在
     */
    public static long ttl(String key) {
        try (Jedis jedis = getJedis()) {
            return jedis.ttl(key);
        } catch (Exception e) {
            log.error("Redis获取键剩余过期时间失败: key={}", key, e);
            return -2;
        }
    }

    /**
     * 哈希表设置字段值
     *
     * @param key   键
     * @param field 字段
     * @param value 值
     * @return 是否成功
     */
    public static boolean hset(String key, String field, String value) {
        try (Jedis jedis = getJedis()) {
            return jedis.hset(key, field, value) >= 0;
        } catch (Exception e) {
            log.error("Redis哈希表设置字段值失败: key={}, field={}, value={}", key, field, value, e);
            return false;
        }
    }

    /**
     * 哈希表获取字段值
     *
     * @param key   键
     * @param field 字段
     * @return 字段值
     */
    public static String hget(String key, String field) {
        try (Jedis jedis = getJedis()) {
            return jedis.hget(key, field);
        } catch (Exception e) {
            log.error("Redis哈希表获取字段值失败: key={}, field={}", key, field, e);
            return null;
        }
    }

    /**
     * 哈希表获取所有字段和值
     *
     * @param key 键
     * @return 所有字段和值的映射
     */
    public static Map<String, String> hgetAll(String key) {
        try (Jedis jedis = getJedis()) {
            return jedis.hgetAll(key);
        } catch (Exception e) {
            log.error("Redis哈希表获取所有字段和值失败: key={}", key, e);
            return null;
        }
    }

    /**
     * 哈希表删除字段
     *
     * @param key   键
     * @param field 字段
     * @return 删除的字段数量
     */
    public static Long hdel(String key, String field) {
        try (Jedis jedis = getJedis()) {
            return jedis.hdel(key, field);
        } catch (Exception e) {
            log.error("Redis哈希表删除字段失败: key={}, field={}", key, field, e);
            return 0L;
        }
    }

    /**
     * 列表左侧推入元素
     *
     * @param key   键
     * @param value 值
     * @return 列表长度
     */
    public static Long lpush(String key, String value) {
        try (Jedis jedis = getJedis()) {
            return jedis.lpush(key, value);
        } catch (Exception e) {
            log.error("Redis列表左侧推入元素失败: key={}, value={}", key, value, e);
            return 0L;
        }
    }

    /**
     * 列表右侧弹出元素
     *
     * @param key 键
     * @return 弹出的元素
     */
    public static String rpop(String key) {
        try (Jedis jedis = getJedis()) {
            return jedis.rpop(key);
        } catch (Exception e) {
            log.error("Redis列表右侧弹出元素失败: key={}", key, e);
            return null;
        }
    }

    /**
     * 获取列表长度
     *
     * @param key 键
     * @return 列表长度
     */
    public static Long llen(String key) {
        try (Jedis jedis = getJedis()) {
            return jedis.llen(key);
        } catch (Exception e) {
            log.error("Redis获取列表长度失败: key={}", key, e);
            return 0L;
        }
    }
}

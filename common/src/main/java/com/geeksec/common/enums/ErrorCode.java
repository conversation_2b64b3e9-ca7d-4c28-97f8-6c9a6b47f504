package com.geeksec.common.enums;

import lombok.Getter;

/**
 * 业务结果状态码和错误码枚举
 * 
 * <p>
 * 定义业务相关的状态码和错误码，用于表示业务逻辑执行的结果状态。
 * </p>
 * 
 * <p>
 * 状态码范围：
 * <ul>
 * <li>40xxx: 业务通用错误</li>
 * <li>50xxx: 系统级错误</li>
 * <li>60xxx: 业务逻辑错误</li>
 * <li>70xxx: 认证授权错误</li>
 * <li>80xxx: 系统操作错误</li>
 * <li>90xxx: 业务特定错误</li>
 * </ul>
 * </p>
 *
 * <AUTHOR>
 * @since 3.0.0
 */
@Getter
public enum ErrorCode {
    // ========== 业务通用错误 40xxx ==========
    /** 业务处理失败 */
    BUSINESS_ERROR(40000, "业务处理失败"),
    /** 数据验证失败 */
    VALIDATION_ERROR(40001, "数据验证失败"),
    /** 数据不存在 */
    DATA_NOT_FOUND(40002, "数据不存在"),
    /** 数据已存在 */
    DATA_ALREADY_EXISTS(40003, "数据已存在"),
    /** 操作失败 */
    OPERATION_FAILED(40004, "操作失败"),
    /** 状态错误 */
    STATUS_ERROR(40005, "状态错误"),
    /** 配置错误 */
    CONFIG_ERROR(40006, "配置错误"),
    /** 文件操作错误 */
    FILE_ERROR(40007, "文件操作错误"),
    /** 网络错误 */
    NETWORK_ERROR(40008, "网络错误"),

    // ========== 系统级错误 50xxx ==========
    /** 数据库错误 */
    DATABASE_ERROR(50001, "数据库错误"),
    /** 缓存错误 */
    CACHE_ERROR(50002, "缓存错误"),
    /** 外部服务错误 */
    EXTERNAL_SERVICE_ERROR(50003, "外部服务错误"),
    /** 消息队列错误 */
    MESSAGE_QUEUE_ERROR(50004, "消息队列错误"),

    // ========== 参数验证错误 60xxx ==========
    /** 参数验证失败 */
    PARAM_VALIDATION_FAILED(60001, "参数验证失败"),
    /** 数据状态异常 */
    DATA_STATUS_ERROR(60002, "数据状态异常"),
    /** 业务规则验证失败 */
    BUSINESS_RULE_VIOLATION(60003, "业务规则验证失败"),

    // ========== 认证授权错误 70xxx ==========
    /** 用户不存在 */
    USER_NOT_FOUND(70001, "用户不存在"),
    /** 用户名或密码错误 */
    INVALID_CREDENTIALS(70002, "用户名或密码错误"),
    /** 账户被锁定 */
    ACCOUNT_LOCKED(70003, "账户被锁定"),
    /** 账户已禁用 */
    ACCOUNT_DISABLED(70004, "账户已禁用"),
    /** 密码已过期 */
    PASSWORD_EXPIRED(70005, "密码已过期"),
    /** Token无效 */
    INVALID_TOKEN(70006, "Token无效"),
    /** Token已过期 */
    TOKEN_EXPIRED(70007, "Token已过期"),
    /** 权限不足 */
    INSUFFICIENT_PERMISSIONS(70008, "权限不足"),
    /** token为空 */
    TOKEN_EMPTY(71001, "token为空,请重新登录"),
    /** 登录已过期 */
    LOGIN_EXPIRE(71002, "登录已过期,请重新登录"),
    /** 当前登录的用户信息为空 */
    LOGIN_STATUS_CHECK_USER_EMPTY(71008, "当前登录的用户信息为空"),
    /** 用户登录状态校验异常 */
    LOGIN_STATUS_CHECK_ERROR(71013, "用户登录状态校验异常"),
    /** 权限校验系统请求失败 */
    LOGIN_STATUS_CHECK_REQUEST_FAIL(71014, "权限校验系统请求失败"),
    /** 用户不存在 */
    USER_NOT_EXIST(80034, "用户不存在"),
    /** 用户未登录 */
    USER_NOT_LOGIN(80035, "用户未登录"),
    /** 修改密码失败：原密码输入错误 */
    OLD_PASSWORD_ERROR(80031, "修改密码失败：原密码输入错误"),
    /** 修改密码失败：新密码与原始密码相同 */
    OLD_PASSWORD_REPEAT(80032, "修改密码失败：新密码与原始密码相同"),
    /** 修改密码失败 */
    MODIFY_USER_PASSWORD_ERROR(80033, "修改密码失败"),

    // ========== 参数验证相关错误 71xxx ==========
    /** 请求参数格式异常 */
    REQUEST_PARAM_ERROR(71003, "请求参数格式异常,请检查"),
    /** 请求参数为空 */
    REQUEST_PARAM_EMPTY(71004, "请求参数为空,请检查"),
    /** 缺少必填参数 */
    REQUEST_PARAM_LEAK(71005, "缺少必填参数"),
    /** IP类型查询参数格式异常 */
    QUERY_IP_PARAM_ERROR(71006, "IP类型查询参数格式异常,请检查"),
    /** IP地址格式错误 */
    IP_FORMAT_ERROR(71007, "IP地址格式错误，请检查"),
    /** 端口参数错误 */
    PORT_FORMAT_ERROR(71009, "端口参数错误"),
    /** 协议规则参数格式错误 */
    RULE_PROTOCOL_FORMAT_ERROR(71010, "协议规则参数格式错误"),
    /** 动态库检验文件为空 */
    CHECK_SO_EMPTY(71011, "动态库检验文件为空"),
    /** 动态库检验失败 */
    CHECK_SO_FAIL(71012, "动态库检验失败"),
    /** 缺少必填参数 */
    MISSING_REQUIRED_PARAMETERS(90058, "缺少必填参数"),

    // ========== 数据库相关错误 80xxx ==========
    /** 数据库连接失败 */
    DATABASE_CONNECTION_FAILED(80001, "数据库连接失败"),
    /** 数据库操作失败 */
    DATABASE_OPERATION_FAILED(80002, "数据库操作失败"),
    /** 数据库执行异常 */
    MYSQL_EXECUTE_ERROR(80028, "数据库执行异常"),
    /** 数据库磁盘模式字段读取异常 */
    DISK_READ_MODEL_LOAD_ERROR(90048, "数据库磁盘模式字段读取异常"),

    // ========== 缓存相关错误 ==========
    /** 缓存操作失败 */
    CACHE_OPERATION_FAILED(80003, "缓存操作失败"),
    /** Redis缓存查询异常 */
    REDIS_QUERY_ERROR(54001, "Redis缓存查询异常"),

    // ========== 系统操作错误 ==========
    /** 消息队列操作失败 */
    MESSAGE_QUEUE_FAILED(80004, "消息队列操作失败"),
    /** 网络连接失败 */
    NETWORK_CONNECTION_FAILED(80006, "网络连接失败"),
    /** 外部服务调用失败 */
    EXTERNAL_SERVICE_FAILED(80007, "外部服务调用失败"),
    /** 配置加载失败 */
    CONFIG_LOAD_FAILED(80008, "配置加载失败"),
    /** 系统信息查询异常 */
    SYSTEM_INFO_QUERY_ERROR(54002, "系统信息查询异常"),
    /** 服务器关机失败 */
    SYSTEM_SHUTDOWN_ERROR(80029, "服务器关机失败"),
    /** 服务器重启失败 */
    SYSTEM_REBOOT_ERROR(80030, "服务器重启失败"),
    /** 查询系统清理状态失败 */
    SYSTEM_CLEAR_STATUS_CHECK_FAIL(90054, "查询系统清理状态失败"),

    // ========== 文件操作相关错误 ==========
    /** 文件操作失败 */
    FILE_OPERATION_FAILED(80005, "文件操作失败"),
    /** 文件下载失败 */
    FILE_DOWNLOAD_FAIL(80009, "文件下载失败"),
    /** 文件不存在 */
    FILE_NOT_EXIST(80010, "文件下载失败：文件不存在"),
    /** 文件下载路径为空 */
    FILE_DOWNLOAD_PATH_EMPTY(80018, "文件下载路径为空"),
    /** 文件上传路径为空 */
    FILE_UPLOAD_PATH_EMPTY(80019, "文件上传路径为空"),
    /** 上传文件为空 */
    UPLOAD_FILE_EMPTY(80020, "上传文件为空"),
    /** 上传文件失败 */
    UPLOAD_FILE_FAIL(80021, "上传文件失败"),
    /** 文件读取失败 */
    FILE_READ_FAILURE(80036, "文件读取失败"),
    /** 文件内容格式非法 */
    FILE_CONTENT_FORMAT_ILLEGAL(54003, "文件内容格式非法"),
    /** 文件创建失败 */
    FILE_CREATION_FAILED(90059, "文件创建失败"),
    /** 临时文件夹位置未配置，请联系管理员 */
    TEMP_FOLDER_LOCATION_NOT_CONFIG(90060, "临时文件夹位置未配置，请联系管理员"),
    /** 导入pcap文件失败 */
    IMPORT_PCAP_FILE_ERROR(90022, "导入pcap文件失败"),

    // ========== 导入导出相关错误 ==========
    /** CSV导出失败 */
    CSV_EXPORT_FAIL(80014, "CSV导出失败"),
    /** 上传导入CSV文件失败 */
    UPLOAD_CSV_FILE_FAIL(80022, "上传导入CSV文件失败"),
    /** 会话日志导出查询失败 */
    SESSION_LOG_EXPORT_QUERY_ERROR(80013, "会话日志导出查询失败：查询日志失败"),
    /** 特征规则模板文件下载失败 */
    FEATURE_RULE_TEMPLATE_DOWNLOAD_FAIL(80023, "特征规则模板文件下载失败"),
    /** 过滤规则模板下载失败 */
    FILTER_RULE_TEMPLATE_DOWNLOAD_FAIL(80025, "过滤规则模板下载失败"),
    /** 下载文件数量过大 */
    DOWNLOAD_FILE_COUNT_OVERATE(80024, "下载文件数量过大"),

    // ========== 任务管理相关错误 ==========
    /** 删除下载任务失败 */
    DELETE_DOWNLOAD_TASK_FAILED(80011, "删除下载任务失败"),
    /** 下载任务不存在 */
    DOWNLOAD_TASK_NOT_EXIST(80012, "下载任务不存在"),
    /** 导入任务创建失败 */
    DOWNLOAD_TASK_CREATE_FAIL(80026, "导入任务创建失败"),
    /** 下载任务删除失败 */
    DELETE_DOWNLOAD_TASK_FAIL(80027, "下载任务删除失败"),
    /** 任务执行失败 */
    TASK_EXECUTION_FAILED(90006, "任务执行失败"),
    /** 任务关联网口异常 */
    TASK_RELATED_NETFLOW_ERROR(90047, "任务关联网口异常"),
    /** 查询任务配置失败 */
    QUERY_TASK_CONFIG_ERROR(90050, "查询任务配置失败"),
    /** 引入脆弱性检测任务失败 */
    IMPORT_VULN_CHECK_TASK_FAIL(90015, "引入脆弱性检测任务失败"),
    /** 离线导入数据导入失败 */
    OFFLINE_TASK_BATCH_ERROR(90024, "离线导入数据导入失败"),

    // ========== ES相关错误 ==========
    /** ES文档查询异常 */
    ES_SEARCH_ERROR(52001, "ES文档查询异常"),
    /** ES聚合查询异常 */
    ES_AGGR_ERROR(52002, "ES聚合查询异常"),
    /** ES文档更新异常 */
    ES_UPDATE_ERROR(52003, "ES文档更新异常"),
    /** ES模板查询异常 */
    ES_TEMPLATE_QUERY_ERROR(52004, "ES模板查询异常"),
    /** ES目标修改标签异常 */
    ES_TARGET_MODIFY_LABEL_ERROR(52005, "ES目标修改标签异常"),
    /** ES操作异常 */
    ES_OPERATE_ERROR(52006, "ES操作异常"),
    /** ES删除异常 */
    ES_DELETE_DOC_ERROR(52007, "ES删除异常"),
    /** ES可查询字段获取异常 */
    ES_QUERY_FIELD_TRANSFER_ERROR(52008, "ES可查询字段获取异常"),

    // ========== 图数据库相关错误 ==========
    /** 图数据库查询异常 */
    GRAPHDB_QUERY_ERROR(53001, "图数据库查询异常"),
    /** 图数据库解析异常 */
    GRAPHDB_PARSE_ERROR(53002, "图数据库解析异常"),
    /** 图数据库查询结果为空 */
    GRAPHDB_QUERY_EMPTY(53003, "图数据库查询结果为空"),
    /** 查询关联节点标签信息失败 */
    GRAPHDB_QUERY_TAG_LABEL_FAIL(53004, "查询关联节点标签信息失败"),
    /** 图数据库历史清空失败 */
    GRAPHDB_QUERY_HISTORY_CLEAR_FAIL(53005, "图数据库历史清空失败"),
    /** 该时间范围无数据 */
    SPACE_NOT_FIND_ERROR(53006, "该时间范围无数据"),

    // ========== 告警相关错误 ==========
    /** 告警指标信息聚合查询异常 */
    ALARM_TARGET_AGGR_ERROR(90030, "告警指标信息聚合查询异常"),
    /** 告警攻击链查询异常 */
    ALARM_ATTACK_CHAIN_QUERY_ERROR(90031, "告警攻击链查询异常"),
    /** 告警列表查询异常 */
    ALARM_LIST_QUERY_ERROR(90032, "告警列表查询异常"),
    /** 告警详情查询异常 */
    ALARM_DETAIL_QUERY_ERROR(90033, "告警详情查询异常"),
    /** 告警研判绘制失败 */
    ALARM_JUDGE_GRAPH_ERROR(90034, "告警研判绘制失败"),
    /** 告警状态切换异常 */
    ALARM_STATUS_UPDATE_ERROR(90035, "告警状态切换异常"),
    /** 告警删除异常 */
    ALARM_DELETE_ERROR(90036, "告警删除异常"),
    /** 生成告警关联会话PCAP下载任务失败 */
    ALARM_PCAP_DOWNLOAD_TASK_FAIL(80015, "生成告警关联会话PCAP下载任务失败"),
    /** 告警列表CSV文件导出失败 */
    ALARM_DATA_CSV_EXPORT_FAIL(80016, "告警列表CSV文件导出失败"),
    /** 告警报告PDF文件导出失败 */
    ALARM_DATA_PDF_EXPORT_FAIL(80017, "告警报告PDF文件导出失败"),

    // ========== 证书相关错误 ==========
    /** 证书解析失败 */
    CERTIFICATE_PARSE_FAILED(90001, "证书解析失败"),
    /** 证书验证失败 */
    CERTIFICATE_VALIDATION_FAILED(90002, "证书验证失败"),
    /** 获取当前用户的证书详情日志模板失败 */
    GET_USER_CERT_LOG_ERROR(90010, "获取当前用户的证书详情日志模板失败"),
    /** 修改当前用户的证书详情日志模板失败 */
    MODIFY_USER_CERT_LOG_ERROR(90011, "修改当前用户的证书详情日志模板失败"),
    /** 修改证书标签失败：证书不能同时为白名单证书和黑名单证书 */
    CERT_BLACK_WHITE_REPEAT(90018, "修改证书标签失败：证书不能同时为白名单证书和黑名单证书"),
    /** 修改证书信息失败：未查找到对应证书 */
    CERT_TAG_MODIFY_EMPTY(90019, "修改证书信息失败：未查找到对应证书"),
    /** 修改证书信息失败 */
    CERT_TAG_MODIFY_ERROR(90020, "修改证书信息失败"),
    /** 根据证书标签查询证书失败 */
    CERT_TAG_QUERY_ERROR(90026, "根据证书标签查询证书失败"),
    /** 证书详情查询失败 */
    CERT_DETAIL_QUERY_ERROR(90038, "证书详情查询失败"),

    // ========== 模型相关错误 ==========
    /** 模型数据冲突 */
    MODEL_NAME_DUPLICATE(90061, "模型名称已存在"),
    /** 模型添加失败 */
    MODEL_ADD_ERROR(90062, "模型添加失败"),
    /** 编辑模型失败 */
    UPDATE_MODEL_ERROR(90063, "编辑模型失败"),
    /** 删除模型失败 */
    MODEL_DELETE_ERROR(90064, "删除模型失败"),
    /** 模型配置验证失败 */
    MODEL_CONFIG_VALIDATION_ERROR(90065, "模型配置验证失败"),
    /** 模型状态切换失败 */
    MODEL_STATE_CHANGE_ERROR(90066, "模型状态切换失败"),

    // ========== 规则相关错误 ==========
    /** 规则引擎执行失败 */
    RULE_ENGINE_FAILED(90007, "规则引擎执行失败"),
    /** 创建过滤规则失败：不支持的过滤类型 */
    FILTER_RULE_TYPE_ERROR(90009, "创建过滤规则失败：不支持的过滤类型"),
    /** 探针规则同步URL未配置 */
    RULE_SYNC_URL_EMPTY(90012, "探针规则同步URL未配置"),
    /** 探针规则同步请求异常 */
    RULE_SYNC_REQUEST_ERROR(90013, "探针规则同步请求异常"),
    /** 特征规则导入失败 */
    FEATURE_IMPORT_ERROR(90025, "特征规则导入失败"),
    /** 特征规则数据冲突 */
    FEATURE_RULE_REPEAT(90027, "特征规则数据冲突"),
    /** 特征规则添加失败 */
    FEATURE_RULE_ADD_ERROR(90028, "特征规则添加失败"),
    /** 过滤规则数据冲突 */
    FILTER_RULE_REPEAT(90029, "过滤规则数据冲突"),
    /** 导入过滤规则失败 */
    FILTER_RULE_IMPORT_ERROR(90044, "导入过滤规则失败"),
    /** 编辑特征规则失败 */
    UPDATE_FEATURE_RULE_ERROR(90045, "编辑特征规则失败"),
    /** 删除特征规则失败 */
    FEATURE_RULE_DELETE_ERROR(90046, "删除特征规则失败"),

    // ========== 会话相关错误 ==========
    /** 会话详情数据解析失败 */
    SESSION_DETAIL_PARSE_ERROR(10001, "会话详情数据解析失败"),
    /** 会话详情基础信息查询失败 */
    SESSION_BASIC_QUERY_ERROR(10002, "会话详情基础信息查询失败"),
    /** 协议元数据查询失败 */
    PROTOCOL_METADATA_QUERY_ERROR(10003, "协议元数据查询失败"),
    /** 会话聚合IP列表查询失败 */
    IP_AGGR_QUERY_ERROR(10004, "会话聚合IP列表查询失败"),
    /** 会话聚合域名列表查询失败 */
    DOMAIN_AGGR_QUERY_ERROR(10005, "会话聚合域名列表查询失败"),
    /** 会话聚合证书列表查询失败 */
    CERT_AGGR_QUERY_ERROR(10006, "会话聚合证书列表查询失败"),
    /** 会话聚合指纹列表查询失败 */
    FINGER_AGGR_QUERY_ERROR(10007, "会话聚合指纹列表查询失败"),
    /** 会话聚合端口列表查询失败 */
    PORT_AGGR_QUERY_ERROR(10008, "会话聚合端口列表查询失败"),
    /** 会话聚合应用列表查询失败 */
    APP_AGGR_QUERY_ERROR(10009, "会话聚合应用列表查询失败"),
    /** 协议元数据列表查询失败 */
    METADATA_LIST_QUERY_ERROR(10010, "协议元数据列表查询失败"),
    /** 会话标签聚合失败 */
    TAG_AGGR_QUERY_ERROR(10011, "会话标签聚合失败"),
    /** 会话列表聚合失败 */
    SESSION_AGGR_QUERY_ERROR(10012, "会话列表聚合失败"),
    /** 会话元数据聚合查询失败 */
    SESSION_METADATA_AGGR_ERROR(10013, "会话元数据聚合查询失败"),
    /** 会话详情查询失败 */
    SESSION_DETAIL_QUERY_ERROR(10014, "会话详情查询失败"),
    /** 修改会话标签失败 */
    UPDATE_LABELS_ERROR(90042, "修改会话标签失败"),
    /** 修改详情备注失败 */
    UPDATE_REMARK_ERROR(90043, "修改详情备注失败"),
    /** 查询会话中通信相关桑基图异常 */
    SANKEY_GRAPH_QUERY_ERROR(90014, "查询会话中通信相关桑基图异常"),
    /** 创建会话查询历史失败 */
    CREATE_QUERY_HISTORY_ERROR(90055, "创建会话查询历史失败"),

    // ========== 查询相关错误 ==========
    /** 创建检索模板失败：至少含有一个查询条件 */
    CREATE_QUERY_TEMPLATE_LEAK(90016, "创建检索模板失败：至少含有一个查询条件"),
    /** 查询检索历史记录列表失败 */
    QUERY_HISTORY_FAILED(90017, "查询检索历史记录列表失败"),
    /** IP详情查询失败 */
    IP_DETAIL_QUERY_ERROR(90037, "IP详情查询失败"),
    /** 域名详情查询失败 */
    DOMAIN_DETAIL_QUERY_ERROR(90039, "域名详情查询失败"),
    /** 区块链详情信息查询失败 */
    BLOCKCHAIN_DETAIL_QUERY_ERROR(90040, "区块链详情信息查询失败"),
    /** 指纹详情信息查询失败 */
    FINGER_DETAIL_QUERY_ERROR(90041, "指纹详情信息查询失败"),
    /** IP态势图查询异常 */
    IP_SITUATION_QUERY_ERROR(90049, "IP态势图查询异常"),
    /** 标签库查询失败 */
    TAG_LIBRARY_QUERY_ERROR(90051, "标签库查询失败"),
    /** 情报列表信息查询失败 */
    THREAT_INFO_QUERY_ERROR(90053, "情报列表信息查询失败"),
    /** 应用服务详情信息查询失败 */
    APPSERVICE_DETAIL_QUERY_ERROR(90056, "应用服务详情信息查询失败"),
    /** 应用详情信息查询失败 */
    APP_DETAIL_QUERY_ERROR(90057, "应用详情信息查询失败"),

    // ========== 探针相关错误 ==========
    /** 探针重启同步成功 */
    PROBE_RESTART_SYNC_SUCCESS(60001, "探针重启同步成功"),
    /** 探针重启同步失败 */
    PROBE_RESTART_SYNC_FAIL(60002, "探针重启同步失败"),
    /** 探针重启同步中 */
    PROBE_RESTART_SYNC_RUNNING(60003, "探针重启同步中"),
    /** 探针重启同步静止 */
    PROBE_RESTART_SYNC_IDLE(60004, "探针重启同步静止"),

    // ========== 其他业务特定错误 ==========
    /** 网络流量分析失败 */
    TRAFFIC_ANALYSIS_FAILED(90003, "网络流量分析失败"),
    /** 威胁检测失败 */
    THREAT_DETECTION_FAILED(90004, "威胁检测失败"),
    /** 数据采集失败 */
    DATA_COLLECTION_FAILED(90005, "数据采集失败"),
    /** 报告生成失败 */
    REPORT_GENERATION_FAILED(90008, "报告生成失败"),
    /** 模型开关修改失败 */
    MODEL_SWITCH_ERROR(90021, "模型开关修改失败"),
    /** IP地址转换失败 */
    IP_NUMERICAL_ERROR(90023, "IP地址转换失败"),
    /** 添加IP内网段失败 */
    ADD_INTERNAL_IP_NET_ERROR(90052, "添加IP内网段失败");

    /** 状态码 */
    private final int code;

    /** 状态消息 */
    private final String message;

    /**
     * 构造函数
     *
     * @param code    状态码
     * @param message 状态消息
     */
    ErrorCode(int code, String message) {
        this.code = code;
        this.message = message;
    }

    /**
     * 根据状态码获取枚举值
     *
     * @param code 状态码
     * @return 对应的枚举值，如果不存在则返回 null
     */
    public static ErrorCode getByCode(int code) {
        for (ErrorCode errorCode : values()) {
            if (errorCode.getCode() == code) {
                return errorCode;
            }
        }
        return null;
    }

    /**
     * 根据状态码获取枚举值，如果不存在则返回默认值
     *
     * @param code         状态码
     * @param defaultValue 默认返回值
     * @return 对应的枚举值，如果不存在则返回指定的默认值
     */
    public static ErrorCode getByCode(int code, ErrorCode defaultValue) {
        ErrorCode result = getByCode(code);
        return result != null ? result : defaultValue;
    }

    /**
     * 判断是否为业务错误（4xxxx-9xxxx）
     *
     * @return 如果是业务错误则返回true
     */
    public boolean isBusinessError() {
        return this.code >= 40000 && this.code < 100000;
    }

    /**
     * 判断是否为客户端错误（4xxxx）
     *
     * @return 如果是客户端错误则返回true
     */
    public boolean isClientError() {
        return this.code >= 40000 && this.code < 50000;
    }

    /**
     * 判断是否为服务器错误（5xxxx）
     *
     * @return 如果是服务器错误则返回true
     */
    public boolean isServerError() {
        return this.code >= 50000 && this.code < 60000;
    }

    /**
     * 判断是否为成功状态
     *
     * @param code 状态码
     * @return 如果状态码表示成功则返回true
     */
    public static boolean isSuccess(Integer code) {
        return code != null && (code >= 200 && code < 300);
    }

    @Override
    public String toString() {
        return String.format("ErrorCode{code=%d, message='%s'}",
                code, message);
    }
}

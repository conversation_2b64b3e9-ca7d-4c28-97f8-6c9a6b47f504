package com.geeksec.common.infrastructure.network;

import java.net.URI;
import java.net.URISyntaxException;
import java.net.URL;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;

import org.apache.commons.lang3.StringUtils;

import com.geeksec.common.utils.crypto.HashUtils;

import lombok.extern.slf4j.Slf4j;

/**
 * 域名工具类（应用层网络概念）
 *
 * 专注于域名和URL应用层处理：域名验证、URL处理、域名解析等
 * 依赖 NetworkUtils 进行基础网络功能，避免代码重复
 * 使用Guava的InternetDomainName类和Apache Commons Validator实现
 *
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Slf4j
public final class DomainUtils {

    /**
     * URL协议分隔符
     */
    private static final String PROTOCOL_SEPARATOR = "://";

    /**
     * 默认HTTP协议前缀
     */
    private static final String DEFAULT_HTTP_PREFIX = "http://";

    /**
     * 顶级域名正则表达式
     */
    private static final Pattern TLD_PATTERN = Pattern.compile(
            "^[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(\\.[a-zA-Z]{2,})+$");

    /**
     * 反向查询域名后缀
     */
    private static final String PTR_SUFFIX = "arpa";

    /**
     * 域名标签最小长度
     */
    private static final int MIN_DOMAIN_PARTS = 2;

    /**
     * 通配符前缀
     */
    private static final String WILDCARD_PREFIX = "*.";

    /**
     * 检测分布式服务部署证书的最小域名数量
     */
    private static final int MIN_COMMON_SUBDOMAIN_COUNT = 4;

    /**
     * 检测分布式服务部署证书的最小重复次数
     */
    private static final int MIN_SUBDOMAIN_REPEAT_COUNT = 3;

    /**
     * 域名部分索引：第一部分
     */
    private static final int DOMAIN_PART_FIRST = 1;

    /**
     * 域名部分索引：第二部分
     */
    private static final int DOMAIN_PART_SECOND = 2;

    /**
     * 域名长度限制
     */
    private static final int MAX_DOMAIN_LENGTH = 200;

    /**
     * 私有构造函数，防止实例化
     */
    private DomainUtils() {
        throw new AssertionError("工具类不需要实例化");
    }

    /**
     * 判断字符串是否为有效的URL
     * 使用简单的URL格式验证
     *
     * @param url 要验证的URL字符串
     * @return 如果是有效的URL则返回true，否则返回false
     */
    public static boolean isUrl(String url) {
        if (StringUtils.isEmpty(url)) {
            return false;
        }

        try {
            new URL(url);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 从URL中提取域名
     * 支持标准的URL格式，如http://example.com/path
     *
     * @param url 要提取域名的URL字符串
     * @return 提取的域名，如果URL无效则返回null
     */
    public static String extractDomainFromUrl(String url) {
        if (StringUtils.isEmpty(url)) {
            return null;
        }

        try {
            // 如果URL不包含协议，添加默认协议
            String processedUrl = url;
            if (!url.contains(PROTOCOL_SEPARATOR)) {
                processedUrl = DEFAULT_HTTP_PREFIX + url;
            }

            URL urlObj = new URL(processedUrl);
            return urlObj.getHost();
        } catch (Exception e) {
            log.debug("从URL提取域名失败: {}", url, e);
            return null;
        }
    }

    /**
     * 判断是否为PTR域名（反向DNS查询域名）
     * PTR域名通常以.arpa结尾
     *
     * @param domain 要检查的域名
     * @return 如果是PTR域名则返回true
     */
    public static boolean isPtrDomain(String domain) {
        if (StringUtils.isEmpty(domain)) {
            return false;
        }

        String[] domainParts = domain.split("\\.");
        if (domainParts.length == 0) {
            return false;
        }

        String suffix = domainParts[domainParts.length - 1];
        return PTR_SUFFIX.equalsIgnoreCase(suffix);
    }

    /**
     * 判断当前域名是否有效
     * 该方法提供完整的域名验证，包括：
     * 1. 基本的域名格式验证
     * 2. 检查是否为IP地址
     * 3. 检查是否为PTR请求
     *
     * @param domain 域名字符串
     * @return 如果是有效的域名则返回true，否则返回false
     */
    public static boolean isValidDomain(String domain) {
        // 判断是否为空
        if (StringUtils.isEmpty(domain)) {
            return false;
        }

        // 判断是否为IP类型
        if (NetworkUtils.isValidIp(domain)) {
            return false;
        }

        // 处理通配符域名
        String processedDomain = domain;
        if (domain.startsWith(WILDCARD_PREFIX)) {
            processedDomain = domain.substring(WILDCARD_PREFIX.length());
        }

        // 使用简单的域名格式验证
        try {
            new URI("http://" + processedDomain);
            if (!TLD_PATTERN.matcher(processedDomain).matches()) {
                return false;
            }
        } catch (URISyntaxException e) {
            return false;
        }

        // 判断是否为PTR请求
        if (isPtrDomain(processedDomain)) {
            return false;
        }

        return true;
    }

    /**
     * 严格验证域名是否有效
     * 这个方法比isValidDomain更严格，会检查TLD格式
     *
     * @param domain 要验证的域名
     * @return 如果域名有效则返回true
     */
    public static boolean isValidDomainStrict(String domain) {
        if (StringUtils.isEmpty(domain)) {
            return false;
        }

        // 使用更严格的验证：必须符合TLD模式
        return isValidDomain(domain) && TLD_PATTERN.matcher(domain).matches();
    }

    /**
     * 判断SNI域名是否有效
     * 该方法除了进行域名验证外，还会检查域名是否与服务器IP相同
     *
     * @param domain   域名字符串
     * @param serverIp 服务器IP
     * @return 如果是有效的SNI域名则返回true，否则返回false
     */
    public static boolean isValidSniDomain(String domain, String serverIp) {
        // 判断是否为IP类型
        if (NetworkUtils.isValidIp(domain)) {
            // 如果是IP类型且与服务器IP相同，返回false
            return !domain.equals(serverIp);
        }

        // 进行完整的域名验证
        if (!isValidDomain(domain)) {
            return false;
        }

        // 处理通配符域名
        String processedDomain = domain;
        if (domain.startsWith(WILDCARD_PREFIX)) {
            processedDomain = domain.substring(WILDCARD_PREFIX.length());
        }

        // 判断当前域名长度是否正确
        String[] domainParts = processedDomain.split("\\.");
        return domainParts.length >= MIN_DOMAIN_PARTS;
    }

    /**
     * 检查域名是否包含通配符（如*.example.com）
     *
     * @param domain 要检查的域名
     * @return 如果包含通配符则返回true
     */
    public static boolean isWildcardDomain(String domain) {
        if (StringUtils.isEmpty(domain)) {
            return false;
        }
        return domain.startsWith(WILDCARD_PREFIX);
    }

    /**
     * 从通配符域名中提取根域名
     * 例如：从*.example.com提取example.com
     *
     * @param wildcardDomain 通配符域名
     * @return 提取的根域名，如果不是通配符域名则返回原始域名
     */
    public static String extractFromWildcardDomain(String wildcardDomain) {
        if (isWildcardDomain(wildcardDomain)) {
            return wildcardDomain.substring(WILDCARD_PREFIX.length()); // 移除"*."
        }
        return wildcardDomain;
    }

    /**
     * 检查子域名是否匹配通配符域名
     * 例如：test.example.com匹配*.example.com
     *
     * @param domain          要检查的域名
     * @param wildcardPattern 通配符域名模式
     * @return 如果匹配则返回true
     */
    public static boolean matchesWildcardDomain(String domain, String wildcardPattern) {
        if (StringUtils.isEmpty(domain) || StringUtils.isEmpty(wildcardPattern)) {
            return false;
        }

        if (!isWildcardDomain(wildcardPattern)) {
            return domain.equals(wildcardPattern);
        }

        String rootPattern = extractFromWildcardDomain(wildcardPattern);
        return domain.endsWith(rootPattern) && domain.length() > rootPattern.length() &&
                domain.charAt(domain.length() - rootPattern.length() - 1) == '.';
    }

    /**
     * 判断是否为子域名
     *
     * @param domain       域名
     * @param parentDomain 父域名
     * @return 如果是子域名则返回true
     */
    public static boolean isSubdomain(String domain, String parentDomain) {
        if (StringUtils.isEmpty(domain) || StringUtils.isEmpty(parentDomain)) {
            return false;
        }
        try {
            URI domainUri = new URI("http://" + domain);
            URI parentUri = new URI("http://" + parentDomain);
            String domainHost = domainUri.getHost();
            String parentHost = parentUri.getHost();
            if (domainHost == null || parentHost == null) {
                return false;
            }
            return domainHost.endsWith("." + parentHost);
        } catch (URISyntaxException e) {
            log.debug("解析域名失败: {} 或 {}", domain, parentDomain, e);
            return false;
        }
    }

    /**
     * 提取根域名（二级域名）
     * 使用简单的字符串处理实现
     *
     * @param domain 域名字符串
     * @return 提取的根域名，如果域名无效则返回原始域名
     */
    public static String extractRootDomain(String domain) {
        // 空值检查
        if (StringUtils.isEmpty(domain)) {
            return domain;
        }

        // 移除可能的通配符前缀
        String processedDomain = domain;
        if (domain.startsWith(WILDCARD_PREFIX)) {
            processedDomain = domain.substring(WILDCARD_PREFIX.length());
        }

        try {
            // 简单的根域名提取：取最后两个部分
            String[] parts = processedDomain.split("\\.");
            if (parts.length >= 2) {
                return parts[parts.length - 2] + "." + parts[parts.length - 1];
            }
            return processedDomain;
        } catch (Exception e) {
            // 如果方法失败，返回原始域名
            log.debug("提取根域名失败: {}", domain, e);
            return domain;
        }
    }

    /**
     * 获取域名的根域名（不包含子域名）
     * 例如：从sub.example.com获取example.com
     * 这是extractRootDomain的别名方法，保持向后兼容
     *
     * @param domain 域名
     * @return 根域名，如果解析失败则返回原始域名
     */
    public static String getRootDomain(String domain) {
        return extractRootDomain(domain);
    }

    /**
     * 检查域名列表中是否有共同的子域名模式
     * 用于检测分布式服务部署证书
     *
     * @param domains 域名列表
     * @return 如果有共同的子域名模式则返回true
     */
    public static boolean hasCommonSubdomainPattern(List<String> domains) {
        if (domains == null || domains.size() < MIN_COMMON_SUBDOMAIN_COUNT) {
            return false;
        }

        Map<String, Integer> subdomainCounts = new HashMap<>();

        for (String domain : domains) {
            String[] parts = domain.split("\\.");
            int length = parts.length;

            // 构建子域名部分，排除第一部分和最后一部分
            StringBuilder subdomain = new StringBuilder();
            for (int i = DOMAIN_PART_FIRST; i < length - DOMAIN_PART_FIRST; i++) {
                subdomain.append(parts[i]);
                if (i < length - DOMAIN_PART_SECOND) {
                    subdomain.append(".");
                }
            }

            // 统计每个子域名的出现次数
            String subdomainStr = subdomain.toString();
            if (StringUtils.isNotEmpty(subdomainStr)) {
                subdomainCounts.put(subdomainStr, subdomainCounts.getOrDefault(subdomainStr, 0) + 1);
            }
        }

        // 检查是否有任何子域名的出现次数超过最小重复次数
        for (Integer count : subdomainCounts.values()) {
            if (count > MIN_SUBDOMAIN_REPEAT_COUNT) {
                return true;
            }
        }
        return false;
    }

    /**
     * 判断域名是否为IP格式
     *
     * @param domain 域名字符串
     * @return 如果是IP格式则返回true
     */
    public static boolean isDomainIpFormat(String domain) {
        return NetworkUtils.isValidIp(domain);
    }

    /**
     * 格式化域名，如果长度超过限制则使用MD5进行哈希
     *
     * @param domain 域名字符串
     * @return 格式化后的域名
     */
    public static String formatDomain(String domain) {
        if (StringUtils.isEmpty(domain)) {
            return domain;
        }

        if (domain.length() > MAX_DOMAIN_LENGTH) {
            domain = HashUtils.md5(domain);
        }
        return domain;
    }

    /**
     * SNI域名验证的别名方法，保持向后兼容
     *
     * @param domainAddr SNI域名地址
     * @param serverIp   服务器IP
     * @return 如果有效则返回true，否则返回false
     */
    public static Boolean sniValidDomain(String domainAddr, String serverIp) {
        return isValidSniDomain(domainAddr, serverIp);
    }
}

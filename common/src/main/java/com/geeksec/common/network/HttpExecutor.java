package com.geeksec.common.infrastructure.network;

import lombok.extern.slf4j.Slf4j;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.Map;

/**
 * HTTP 请求执行器
 * <p>
 * 提供发送 GET 和 POST 请求的静态方法，并返回一个包含状态码和响应体的 {@link HttpResponse} 对象。
 * 此类取代了功能有限且设计不佳的 HttpUtils。
 *
 * <AUTHOR>
 */
@Slf4j
public final class HttpExecutor {

    private static final int DEFAULT_CONNECT_TIMEOUT = 5000; // 5秒
    private static final int DEFAULT_READ_TIMEOUT = 10000; // 10秒
    private static final String DEFAULT_USER_AGENT = "GeekSec-NTA/3.0";

    private HttpExecutor() {
        throw new AssertionError("工具类不应被实例化");
    }

    /**
     * 发送一个 GET 请求。
     *
     * @param url     请求的 URL
     * @param headers 请求头 (可以为 null)
     * @return 一个 {@link HttpResponse} 对象
     */
    public static HttpResponse get(String url, Map<String, String> headers) {
        HttpURLConnection conn = null;
        try {
            URL requestUrl = new URL(url);
            conn = (HttpURLConnection) requestUrl.openConnection();
            conn.setRequestMethod("GET");
            conn.setConnectTimeout(DEFAULT_CONNECT_TIMEOUT);
            conn.setReadTimeout(DEFAULT_READ_TIMEOUT);
            conn.setRequestProperty("User-Agent", DEFAULT_USER_AGENT);

            if (headers != null) {
                headers.forEach(conn::setRequestProperty);
            }

            int statusCode = conn.getResponseCode();
            String body = readResponseBody(conn);
            return new HttpResponse(statusCode, body);

        } catch (IOException e) {
            log.error("发送 GET 请求失败, URL: {}", url, e);
            return new HttpResponse(-1, e.getMessage());
        } finally {
            if (conn != null) {
                conn.disconnect();
            }
        }
    }

    /**
     * 发送一个 POST 请求。
     *
     * @param url         请求的 URL
     * @param headers     请求头 (可以为 null)
     * @param requestBody 请求体字符串
     * @return 一个 {@link HttpResponse} 对象
     */
    public static HttpResponse post(String url, Map<String, String> headers, String requestBody) {
        HttpURLConnection conn = null;
        try {
            URL requestUrl = new URL(url);
            conn = (HttpURLConnection) requestUrl.openConnection();
            conn.setRequestMethod("POST");
            conn.setDoOutput(true);
            conn.setConnectTimeout(DEFAULT_CONNECT_TIMEOUT);
            conn.setReadTimeout(DEFAULT_READ_TIMEOUT);
            conn.setRequestProperty("User-Agent", DEFAULT_USER_AGENT);

            if (headers != null) {
                headers.forEach(conn::setRequestProperty);
            }

            if (requestBody != null && !requestBody.isEmpty()) {
                try (OutputStream os = conn.getOutputStream()) {
                    byte[] input = requestBody.getBytes(StandardCharsets.UTF_8);
                    os.write(input, 0, input.length);
                }
            }

            int statusCode = conn.getResponseCode();
            String body = readResponseBody(conn);
            return new HttpResponse(statusCode, body);

        } catch (IOException e) {
            log.error("发送 POST 请求失败, URL: {}", url, e);
            return new HttpResponse(-1, e.getMessage());
        } finally {
            if (conn != null) {
                conn.disconnect();
            }
        }
    }

    private static String readResponseBody(HttpURLConnection conn) throws IOException {
        InputStream inputStream = null;
        try {
            inputStream = conn.getInputStream();
        } catch (IOException e) {
            // 如果获取输入流失败 (例如 4xx/5xx 响应), 则尝试获取错误流
            inputStream = conn.getErrorStream();
        }

        if (inputStream == null) {
            return null;
        }

        StringBuilder content = new StringBuilder();
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8))) {
            String line;
            while ((line = reader.readLine()) != null) {
                content.append(line);
            }
        }
        return content.toString();
    }
}

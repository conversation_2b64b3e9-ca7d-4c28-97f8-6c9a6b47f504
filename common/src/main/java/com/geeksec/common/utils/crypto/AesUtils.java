package com.geeksec.common.utils.crypto;

import lombok.extern.slf4j.Slf4j;
import org.bouncycastle.jce.provider.BouncyCastleProvider;

import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.security.Security;
import java.util.Objects;

/**
 * AES 对称加密工具类
 * <p>
 * 提供 AES 算法的密钥生成、加密和解密功能。
 * 此类遵循单一职责原则，专注于 AES 加密。
 *
 * <AUTHOR>
 */
@Slf4j
public final class AesUtils {

    private static final String ALGORITHM = "AES";
    private static final String ALGORITHM_PADDING = "AES/CBC/PKCS7Padding"; // 使用 BouncyCastle 的 Padding

    static {
        // 动态注册 BouncyCastleProvider，如果尚未注册
        if (Security.getProvider(BouncyCastleProvider.PROVIDER_NAME) == null) {
            Security.addProvider(new BouncyCastleProvider());
        }
    }

    private AesUtils() {
        throw new AssertionError("工具类不应被实例化");
    }

    /**
     * 生成一个 AES 密钥。
     *
     * @param keySize 密钥长度，可以是 128, 192 或 256 位。
     * @return 生成的 SecretKey，如果算法不支持则返回 null。
     */
    public static SecretKey generateKey(int keySize) {
        if (keySize != 128 && keySize != 192 && keySize != 256) {
            throw new IllegalArgumentException("无效的密钥长度，必须是 128, 192 或 256");
        }
        try {
            KeyGenerator keyGen = KeyGenerator.getInstance(ALGORITHM);
            keyGen.init(keySize, new SecureRandom());
            return keyGen.generateKey();
        } catch (NoSuchAlgorithmException e) {
            log.error("生成 AES 密钥失败，不支持的算法: {}", ALGORITHM, e);
            return null;
        }
    }

    /**
     * 使用指定的密钥加密数据 (AES/ECB/PKCS5Padding)。
     *
     * @param plainText 明文字符串
     * @param key       加密密钥
     * @return Base64 编码的密文字符串，加密失败则返回 null
     */
    public static String encrypt(String plainText, SecretKey key) {
        if (plainText == null || key == null) {
            return null;
        }
        try {
            Cipher cipher = Cipher.getInstance(ALGORITHM);
            cipher.init(Cipher.ENCRYPT_MODE, key);
            byte[] encryptedBytes = cipher.doFinal(plainText.getBytes(StandardCharsets.UTF_8));
            return java.util.Base64.getEncoder().encodeToString(encryptedBytes);
        } catch (Exception e) {
            log.error("AES 加密失败", e);
            return null;
        }
    }

    /**
     * 使用指定的密钥解密数据 (AES/ECB/PKCS5Padding)。
     *
     * @param encryptedText Base64 编码的密文字符串
     * @param key           解密密钥
     * @return 明文字符串，解密失败则返回 null
     */
    public static String decrypt(String encryptedText, SecretKey key) {
        if (encryptedText == null || key == null) {
            return null;
        }
        try {
            byte[] encryptedBytes = java.util.Base64.getDecoder().decode(encryptedText);

            Cipher cipher = Cipher.getInstance(ALGORITHM);
            cipher.init(Cipher.DECRYPT_MODE, key);
            byte[] decryptedBytes = cipher.doFinal(encryptedBytes);
            return new String(decryptedBytes, StandardCharsets.UTF_8);
        } catch (Exception e) {
            log.error("AES 解密失败", e);
            return null;
        }
    }

    /**
     * 使用 Base64 编码的密钥字符串加密数据。
     *
     * @param plainText    明文字符串
     * @param base64Key    Base64 编码的密钥字符串
     * @return Base64 编码的密文字符串
     */
    public static String encrypt(String plainText, String base64Key) {
        byte[] decodedKey = java.util.Base64.getDecoder().decode(base64Key);
        if (decodedKey == null) return null;
        SecretKey key = new SecretKeySpec(decodedKey, 0, decodedKey.length, ALGORITHM);
        return encrypt(plainText, key);
    }

    /**
     * 使用 Base64 编码的密钥字符串解密数据。
     *
     * @param encryptedText Base64 编码的密文字符串
     * @param base64Key     Base64 编码的密钥字符串
     * @return 明文字符串
     */
    public static String decrypt(String encryptedText, String base64Key) {
        byte[] decodedKey = java.util.Base64.getDecoder().decode(base64Key);
        SecretKey key = new SecretKeySpec(decodedKey, 0, decodedKey.length, ALGORITHM);
        return decrypt(encryptedText, key);
    }

    /**
     * 使用 AES/CBC/PKCS7Padding 模式和初始化向量 (IV) 加密数据。
     *
     * @param plainText 明文字符串
     * @param key       加密密钥
     * @param iv        初始化向量 (必须是 16 字节)
     * @return Base64 编码的密文字符串，加密失败则返回 null
     */
    public static String encryptWithIV(String plainText, SecretKey key, IvParameterSpec iv) {
        Objects.requireNonNull(plainText, "明文不能为空");
        Objects.requireNonNull(key, "密钥不能为空");
        Objects.requireNonNull(iv, "初始化向量不能为空");

        try {
            Cipher cipher = Cipher.getInstance(ALGORITHM_PADDING, BouncyCastleProvider.PROVIDER_NAME);
            cipher.init(Cipher.ENCRYPT_MODE, key, iv);
            byte[] encryptedBytes = cipher.doFinal(plainText.getBytes(StandardCharsets.UTF_8));
            return java.util.Base64.getEncoder().encodeToString(encryptedBytes);
        } catch (Exception e) {
            log.error("AES (IV) 加密失败", e);
            return null;
        }
    }

    /**
     * 使用 AES/CBC/PKCS7Padding 模式和初始化向量 (IV) 解密数据。
     *
     * @param encryptedText Base64 编码的密文字符串
     * @param key           解密密钥
     * @param iv            初始化向量 (必须是 16 字节)
     * @return 明文字符串，解密失败则返回 null
     */
    public static String decryptWithIV(String encryptedText, SecretKey key, IvParameterSpec iv) {
        Objects.requireNonNull(encryptedText, "密文不能为空");
        Objects.requireNonNull(key, "密钥不能为空");
        Objects.requireNonNull(iv, "初始化向量不能为空");

        try {
            byte[] encryptedBytes = java.util.Base64.getDecoder().decode(encryptedText);

            Cipher cipher = Cipher.getInstance(ALGORITHM_PADDING, BouncyCastleProvider.PROVIDER_NAME);
            cipher.init(Cipher.DECRYPT_MODE, key, iv);
            byte[] decryptedBytes = cipher.doFinal(encryptedBytes);
            return new String(decryptedBytes, StandardCharsets.UTF_8);
        } catch (Exception e) {
            log.error("AES (IV) 解密失败", e);
            return null;
        }
    }
}

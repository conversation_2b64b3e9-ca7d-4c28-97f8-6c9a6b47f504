package com.geeksec.common.utils.crypto;

import lombok.extern.slf4j.Slf4j;

import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;

/**
 * DES 对称加密工具类
 * <p>
 * 提供 DES 算法的密钥生成、加密和解密功能。
 * 注意：DES 被认为是不安全的，请优先使用 AES ({@link AesUtils})。
 * 此类遵循单一职责原则，专注于 DES 加密。
 *
 * <AUTHOR>
 */
@Slf4j
public final class DesUtils {

    private static final String ALGORITHM = "DES";

    private DesUtils() {
        throw new AssertionError("工具类不应被实例化");
    }

    /**
     * 生成一个 DES 密钥。
     *
     * @return 生成的 SecretKey，如果算法不支持则返回 null。
     */
    public static SecretKey generateKey() {
        try {
            KeyGenerator keyGen = KeyGenerator.getInstance(ALGORITHM);
            keyGen.init(new SecureRandom());
            return keyGen.generateKey();
        } catch (NoSuchAlgorithmException e) {
            log.error("生成 DES 密钥失败，不支持的算法: {}", ALGORITHM, e);
            return null;
        }
    }

    /**
     * 使用指定的密钥加密数据。
     *
     * @param plainText 明文字符串
     * @param key       加密密钥
     * @return Base64 编码的密文字符串，加密失败则返回 null
     */
    public static String encrypt(String plainText, SecretKey key) {
        if (plainText == null || key == null) {
            return null;
        }
        try {
            Cipher cipher = Cipher.getInstance(ALGORITHM);
            cipher.init(Cipher.ENCRYPT_MODE, key);
            byte[] encryptedBytes = cipher.doFinal(plainText.getBytes(StandardCharsets.UTF_8));
            return java.util.Base64.getEncoder().encodeToString(encryptedBytes);
        } catch (Exception e) {
            log.error("DES 加密失败", e);
            return null;
        }
    }

    /**
     * 使用指定的密钥解密数据。
     *
     * @param encryptedText Base64 编码的密文字符串
     * @param key           解密密钥
     * @return 明文字符串，解密失败则返回 null
     */
    public static String decrypt(String encryptedText, SecretKey key) {
        if (encryptedText == null || key == null) {
            return null;
        }
        try {
            byte[] encryptedBytes = java.util.Base64.getDecoder().decode(encryptedText);

            Cipher cipher = Cipher.getInstance(ALGORITHM);
            cipher.init(Cipher.DECRYPT_MODE, key);
            byte[] decryptedBytes = cipher.doFinal(encryptedBytes);
            return new String(decryptedBytes, StandardCharsets.UTF_8);
        } catch (Exception e) {
            log.error("DES 解密失败", e);
            return null;
        }
    }

    /**
     * 使用 Base64 编码的密钥字符串加密数据。
     *
     * @param plainText    明文字符串
     * @param base64Key    Base64 编码的密钥字符串
     * @return Base64 编码的密文字符串
     */
    public static String encrypt(String plainText, String base64Key) {
        byte[] decodedKey = java.util.Base64.getDecoder().decode(base64Key);
        SecretKey key = new SecretKeySpec(decodedKey, 0, decodedKey.length, ALGORITHM);
        return encrypt(plainText, key);
    }

    /**
     * 使用 Base64 编码的密钥字符串解密数据。
     *
     * @param encryptedText Base64 编码的密文字符串
     * @param base64Key     Base64 编码的密钥字符串
     * @return 明文字符串
     */
    public static String decrypt(String encryptedText, String base64Key) {
        byte[] decodedKey = java.util.Base64.getDecoder().decode(base64Key);
        SecretKey key = new SecretKeySpec(decodedKey, 0, decodedKey.length, ALGORITHM);
        return decrypt(encryptedText, key);
    }
}

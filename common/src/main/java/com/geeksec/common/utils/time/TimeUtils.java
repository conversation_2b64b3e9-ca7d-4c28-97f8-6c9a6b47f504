package com.geeksec.common.utils.time;

import com.geeksec.common.constants.CommonConstants;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.Date;
import java.util.Objects;

/**
 * 时间工具类
 * 
 * 提供统一的时间处理功能：时间获取、格式化、解析、转换、计算等
 *
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
public final class TimeUtils {

    private TimeUtils() {
        // 工具类，禁止实例化
    }

    /** 默认时区 */
    private static final ZoneId DEFAULT_ZONE = ZoneId.systemDefault();

    /** 常用日期时间格式化器 */
    public static final DateTimeFormatter DEFAULT_DATETIME_FORMATTER =
            DateTimeFormatter.ofPattern(CommonConstants.DateFormat.DATETIME);
    public static final DateTimeFormatter DEFAULT_DATE_FORMATTER =
            DateTimeFormatter.ofPattern(CommonConstants.DateFormat.DATE);
    public static final DateTimeFormatter DEFAULT_TIME_FORMATTER =
            DateTimeFormatter.ofPattern(CommonConstants.DateFormat.TIME);
    public static final DateTimeFormatter TIMESTAMP_FORMATTER =
            DateTimeFormatter.ofPattern(CommonConstants.DateFormat.TIMESTAMP);

    /** 兼容性别名 */
    public static final String DEFAULT_DATE_TIME_FORMAT = CommonConstants.DateFormat.DATETIME;
    public static final DateTimeFormatter DORIS_DATETIME_FORMATTER = DEFAULT_DATETIME_FORMATTER;

    // ==================== 当前时间获取 ====================

    /**
     * 获取当前时间
     * 
     * @return 当前LocalDateTime
     */
    public static LocalDateTime now() {
        return LocalDateTime.now();
    }

    /**
     * 获取当前日期
     * 
     * @return 当前LocalDate
     */
    public static LocalDate today() {
        return LocalDate.now();
    }

    /**
     * 获取当前时间
     * 
     * @return 当前LocalTime
     */
    public static LocalTime timeNow() {
        return LocalTime.now();
    }

    /**
     * 获取当前时间戳（秒）
     * 
     * @return 当前时间戳（秒）
     */
    public static long nowAsSeconds() {
        return System.currentTimeMillis() / 1000;
    }

    /**
     * 获取当前时间戳（毫秒）
     * 
     * @return 当前时间戳（毫秒）
     */
    public static long nowAsMillis() {
        return System.currentTimeMillis();
    }

    // ==================== 时间格式化 ====================

    /**
     * 格式化LocalDateTime为字符串
     * 
     * @param dateTime 日期时间
     * @return 格式化后的字符串
     */
    public static String format(LocalDateTime dateTime) {
        return dateTime != null ? dateTime.format(DEFAULT_DATETIME_FORMATTER) : null;
    }

    /**
     * 格式化LocalDateTime为字符串
     * 
     * @param dateTime 日期时间
     * @param formatter 格式化器
     * @return 格式化后的字符串
     */
    public static String format(LocalDateTime dateTime, DateTimeFormatter formatter) {
        return dateTime != null ? dateTime.format(formatter) : null;
    }

    /**
     * 格式化LocalDate为字符串
     * 
     * @param date 日期
     * @return 格式化后的字符串
     */
    public static String format(LocalDate date) {
        return date != null ? date.format(DEFAULT_DATE_FORMATTER) : null;
    }

    /**
     * 格式化LocalTime为字符串
     * 
     * @param time 时间
     * @return 格式化后的字符串
     */
    public static String format(LocalTime time) {
        return time != null ? time.format(DEFAULT_TIME_FORMATTER) : null;
    }

    /**
     * 时间戳转字符串时间
     * 
     * @param timestamp 时间戳（毫秒）
     * @return 格式化后的时间字符串
     */
    public static String formatTimestamp(long timestamp) {
        try {
            LocalDateTime dateTime = LocalDateTime.ofInstant(
                Instant.ofEpochMilli(timestamp), DEFAULT_ZONE);
            return format(dateTime);
        } catch (Exception e) {
            log.warn("时间戳格式化失败: {}", timestamp, e);
            return null;
        }
    }

    // ==================== 时间解析 ====================

    /**
     * 解析字符串为LocalDateTime
     * 
     * @param dateTimeStr 日期时间字符串
     * @return LocalDateTime对象
     */
    public static LocalDateTime parse(String dateTimeStr) {
        if (StringUtils.isEmpty(dateTimeStr)) {
            return null;
        }
        try {
            return LocalDateTime.parse(dateTimeStr, DEFAULT_DATETIME_FORMATTER);
        } catch (DateTimeParseException e) {
            log.warn("日期时间解析失败: {}", dateTimeStr, e);
            return null;
        }
    }

    /**
     * 解析字符串为LocalDateTime
     * 
     * @param dateTimeStr 日期时间字符串
     * @param formatter 格式化器
     * @return LocalDateTime对象
     */
    public static LocalDateTime parse(String dateTimeStr, DateTimeFormatter formatter) {
        if (StringUtils.isEmpty(dateTimeStr)) {
            return null;
        }
        try {
            return LocalDateTime.parse(dateTimeStr, formatter);
        } catch (DateTimeParseException e) {
            log.warn("日期时间解析失败: {} with pattern: {}", dateTimeStr, formatter, e);
            return null;
        }
    }

    /**
     * 解析字符串为LocalDate
     * 
     * @param dateStr 日期字符串
     * @return LocalDate对象
     */
    public static LocalDate parseDate(String dateStr) {
        if (StringUtils.isEmpty(dateStr)) {
            return null;
        }
        try {
            return LocalDate.parse(dateStr, DEFAULT_DATE_FORMATTER);
        } catch (DateTimeParseException e) {
            log.warn("日期解析失败: {}", dateStr, e);
            return null;
        }
    }

    /**
     * 解析字符串为LocalTime
     * 
     * @param timeStr 时间字符串
     * @return LocalTime对象
     */
    public static LocalTime parseTime(String timeStr) {
        if (StringUtils.isEmpty(timeStr)) {
            return null;
        }
        try {
            return LocalTime.parse(timeStr, DEFAULT_TIME_FORMATTER);
        } catch (DateTimeParseException e) {
            log.warn("时间解析失败: {}", timeStr, e);
            return null;
        }
    }

    // ==================== 时间戳转换 ====================

    /**
     * LocalDateTime转时间戳（秒）
     * 
     * @param dateTime 日期时间
     * @return 时间戳（秒）
     */
    public static long toTimestamp(LocalDateTime dateTime) {
        return dateTime != null ? dateTime.atZone(DEFAULT_ZONE).toEpochSecond() : 0;
    }

    /**
     * LocalDateTime转时间戳（毫秒）
     * 
     * @param dateTime 日期时间
     * @return 时间戳（毫秒）
     */
    public static long toTimestampMillis(LocalDateTime dateTime) {
        return dateTime != null ? dateTime.atZone(DEFAULT_ZONE).toInstant().toEpochMilli() : 0;
    }

    /**
     * 时间戳（秒）转LocalDateTime
     * 
     * @param timestamp 时间戳（秒）
     * @return LocalDateTime对象
     */
    public static LocalDateTime fromTimestamp(long timestamp) {
        try {
            return LocalDateTime.ofInstant(Instant.ofEpochSecond(timestamp), DEFAULT_ZONE);
        } catch (Exception e) {
            log.warn("时间戳转换失败: {}", timestamp, e);
            return null;
        }
    }

    /**
     * 时间戳（毫秒）转LocalDateTime
     * 
     * @param timestampMillis 时间戳（毫秒）
     * @return LocalDateTime对象
     */
    public static LocalDateTime fromTimestampMillis(long timestampMillis) {
        try {
            return LocalDateTime.ofInstant(Instant.ofEpochMilli(timestampMillis), DEFAULT_ZONE);
        } catch (Exception e) {
            log.warn("时间戳转换失败: {}", timestampMillis, e);
            return null;
        }
    }

    /**
     * Date转LocalDateTime
     * 
     * @param date Date对象
     * @return LocalDateTime对象
     */
    public static LocalDateTime toLocalDateTime(Date date) {
        Objects.requireNonNull(date, "输入日期不能为 null");
        return date.toInstant().atZone(DEFAULT_ZONE).toLocalDateTime();
    }

    /**
     * LocalDateTime转Date
     * 
     * @param localDateTime LocalDateTime对象
     * @return Date对象
     */
    public static Date toDate(LocalDateTime localDateTime) {
        Objects.requireNonNull(localDateTime, "输入日期时间不能为 null");
        return Date.from(localDateTime.atZone(DEFAULT_ZONE).toInstant());
    }

    // ==================== 时间计算 ====================

    /**
     * 获取一天的开始时间（00:00:00）
     * 
     * @param date 日期
     * @return 一天的开始时间
     */
    public static LocalDateTime getStartOfDay(LocalDate date) {
        return date != null ? date.atStartOfDay() : null;
    }

    /**
     * 获取一天的结束时间（23:59:59.999）
     * 
     * @param date 日期
     * @return 一天的结束时间
     */
    public static LocalDateTime getEndOfDay(LocalDate date) {
        return date != null ? date.atTime(LocalTime.MAX) : null;
    }

    /**
     * 获取本月第一天
     * 
     * @param date 日期
     * @return 本月第一天
     */
    public static LocalDate getStartOfMonth(LocalDate date) {
        return date != null ? date.withDayOfMonth(1) : null;
    }

    /**
     * 获取本月最后一天
     * 
     * @param date 日期
     * @return 本月最后一天
     */
    public static LocalDate getEndOfMonth(LocalDate date) {
        return date != null ? date.withDayOfMonth(date.lengthOfMonth()) : null;
    }

    /**
     * 计算两个时间之间的天数差
     * 
     * @param start 开始时间
     * @param end 结束时间
     * @return 天数差
     */
    public static long daysBetween(LocalDate start, LocalDate end) {
        if (start == null || end == null) {
            return 0;
        }
        return Duration.between(start.atStartOfDay(), end.atStartOfDay()).toDays();
    }

    /**
     * 计算两个时间之间的小时差
     * 
     * @param start 开始时间
     * @param end 结束时间
     * @return 小时差
     */
    public static long hoursBetween(LocalDateTime start, LocalDateTime end) {
        if (start == null || end == null) {
            return 0;
        }
        return Duration.between(start, end).toHours();
    }

    // ==================== 便捷方法（兼容NtaUtils） ====================

    /**
     * 获取当前时间戳（秒）
     *
     * @return 当前时间戳（秒）
     */
    public static long getCurrentTimestamp() {
        return System.currentTimeMillis() / 1000;
    }

    /**
     * 获取当前时间戳（毫秒）
     *
     * @return 当前时间戳（毫秒）
     */
    public static long getCurrentTimestampMillis() {
        return System.currentTimeMillis();
    }

    /**
     * 格式化当前时间
     *
     * @return 格式化后的当前时间字符串（yyyy-MM-dd HH:mm:ss）
     */
    public static String formatCurrentTime() {
        return format(LocalDateTime.now());
    }

    /**
     * 格式化时间（兼容NtaUtils）
     *
     * @param dateTime 时间对象
     * @return 格式化后的时间字符串，null时返回空字符串
     */
    public static String formatTime(LocalDateTime dateTime) {
        return dateTime != null ? format(dateTime) : "";
    }
}

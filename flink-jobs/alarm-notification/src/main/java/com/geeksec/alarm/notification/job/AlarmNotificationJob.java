package com.geeksec.alarm.notification.job;

import com.geeksec.alarm.notification.config.AlarmNotificationConfig;
import com.geeksec.common.config.ConfigurationManager;
import com.geeksec.nta.alarm.dto.subscription.NotificationResultDto;
import com.geeksec.alarm.notification.pipeline.NotificationPipeline;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.runtime.state.hashmap.HashMapStateBackend;
import org.apache.flink.streaming.api.CheckpointingMode;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.environment.CheckpointConfig;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;

/**
 * 告警通知作业主类
 * 
 * 业务流程：
 * 1. 从 Kafka 接收来自 alarm-processor 的处理后告警数据
 * 2. 从告警服务获取订阅配置，并监听配置变更
 * 3. 根据订阅规则匹配告警并发送通知
 * 4. 支持邮件和 Kafka 两种通知方式
 * 5. 记录通知发送结果
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Slf4j
public class AlarmNotificationJob {
    
    public static void main(String[] args) throws Exception {
        log.info("启动告警通知作业");
        
        try {
            // 1. 获取配置（统一配置管理器 + 命令行参数）
            final ParameterTool parameterTool = ParameterTool.fromArgs(args)
                    .mergeWith(ConfigurationManager.getConfig());

            // 2. 创建配置对象
            final AlarmNotificationConfig config = AlarmNotificationConfig.fromParameterTool(parameterTool);

            // 打印配置信息
            config.printConfig();

            // 3. 创建执行环境
            final StreamExecutionEnvironment env = createExecutionEnvironment(config);

            // 4. 设置全局参数
            env.getConfig().setGlobalJobParameters(parameterTool);

            // 5. 配置检查点
            configureCheckpointing(env, config);

            // 6. 构建通知处理流水线
            NotificationPipeline pipeline = NotificationPipeline.createDefault(config);
            DataStream<NotificationResultDto> notificationResultStream = pipeline.buildPipeline(env);

            // 7. 配置输出（可选：将通知结果写入日志或其他存储）
            configureOutputs(notificationResultStream, config);

            // 8. 执行作业
            log.info("开始执行告警通知作业...");
            env.execute(config.getJobName());
            
            log.info("告警通知作业执行完成");
            
        } catch (Exception e) {
            log.error("告警通知作业执行失败: {}", e.getMessage(), e);
            throw e;
        }
    }
    
    /**
     * 创建执行环境
     */
    private static StreamExecutionEnvironment createExecutionEnvironment(AlarmNotificationConfig config) {
        final StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        
        // 设置并行度
        env.setParallelism(config.getJobParallelism());
        
        // 设置状态后端
        env.setStateBackend(new HashMapStateBackend());
        
        log.info("执行环境创建完成，并行度: {}", config.getJobParallelism());
        return env;
    }
    
    /**
     * 配置检查点
     */
    private static void configureCheckpointing(StreamExecutionEnvironment env, AlarmNotificationConfig config) {
        // 启用检查点
        env.enableCheckpointing(config.getCheckpointInterval());
        
        // 设置检查点模式
        env.getCheckpointConfig().setCheckpointingMode(CheckpointingMode.EXACTLY_ONCE);
        
        // 设置检查点超时时间
        env.getCheckpointConfig().setCheckpointTimeout(config.getCheckpointTimeout());
        
        // 设置最小暂停间隔
        env.getCheckpointConfig().setMinPauseBetweenCheckpoints(config.getMinPauseBetweenCheckpoints());
        
        // 设置最大并发检查点数
        env.getCheckpointConfig().setMaxConcurrentCheckpoints(config.getMaxConcurrentCheckpoints());
        
        // 设置检查点清理策略
        env.getCheckpointConfig().setExternalizedCheckpointCleanup(
                CheckpointConfig.ExternalizedCheckpointCleanup.RETAIN_ON_CANCELLATION);
        
        log.info("检查点配置完成，间隔: {}ms, 超时: {}ms", 
                config.getCheckpointInterval(), config.getCheckpointTimeout());
    }
    
    /**
     * 配置输出
     */
    private static void configureOutputs(DataStream<NotificationResultDto> notificationResultStream, 
                                       AlarmNotificationConfig config) {
        
        // 输出通知结果到日志（用于调试）
        if (config.isPerformanceLogging()) {
            notificationResultStream
                    .map(result -> {
                        log.info("通知发送结果: 订阅={}, 告警={}, 渠道={}, 状态={}, 接收者={}", 
                                result.getSubscriptionId(),
                                result.getAlarmId(),
                                result.getChannelType(),
                                result.getSendStatus(),
                                result.getRecipient());
                        return result;
                    })
                    .name("notification-result-logger")
                    .setParallelism(1);
        }
        
        // 可以添加其他输出，如：
        // - 将通知结果写入数据库
        // - 发送到监控系统
        // - 写入 Kafka 主题供其他系统消费
        
        log.info("输出配置完成");
    }
    
    /**
     * 打印作业信息
     */
    private static void printJobInfo(AlarmNotificationConfig config) {
        log.info("=== 告警通知作业信息 ===");
        log.info("作业名称: {}", config.getJobName());
        log.info("作业并行度: {}", config.getJobParallelism());
        log.info("输入主题: {}", config.getInputTopic());
        log.info("订阅变更主题: {}", config.getSubscriptionChangesTopic());
        log.info("Kafka 服务器: {}", config.getKafkaBootstrapServers());
        log.info("告警服务URL: {}", config.getAlarmServiceBaseUrl());
        log.info("通知并行度: {}", config.getNotificationParallelism());
        log.info("监控启用: {}", config.isMonitoringEnabled());
        log.info("========================");
    }
}

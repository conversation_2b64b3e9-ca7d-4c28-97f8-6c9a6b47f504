package com.geeksec.alarm.notification.source;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.geeksec.alarm.notification.model.Alarm;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.common.serialization.DeserializationSchema;
import org.apache.flink.api.common.typeinfo.TypeInformation;

import java.io.IOException;
import java.nio.charset.StandardCharsets;

/**
 * 告警反序列化器
 * 将 Kafka 消息反序列化为 Alarm 对象
 *
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Slf4j
public class AlarmDeserializer implements DeserializationSchema<Alarm> {
    
    private static final long serialVersionUID = 1L;
    
    private transient ObjectMapper objectMapper;
    
    @Override
    public void open(InitializationContext context) throws Exception {
        this.objectMapper = new ObjectMapper();
        this.objectMapper.registerModule(new JavaTimeModule());
        log.info("告警反序列化器已初始化");
    }
    
    @Override
    public Alarm deserialize(byte[] message) throws IOException {
        if (message == null || message.length == 0) {
            return null;
        }
        
        try {
            String json = new String(message, StandardCharsets.UTF_8);
            log.debug("反序列化告警消息: {}", json);
            
            Alarm alarm = objectMapper.readValue(json, Alarm.class);
            
            // 基本验证
            if (alarm.getAlarmId() == null || alarm.getAlarmId().trim().isEmpty()) {
                log.warn("告警ID为空，跳过处理");
                return null;
            }
            
            return alarm;
            
        } catch (Exception e) {
            log.error("反序列化告警消息失败: {}", e.getMessage(), e);
            return null;
        }
    }
    
    @Override
    public boolean isEndOfStream(Alarm nextElement) {
        return false;
    }
    
    @Override
    public TypeInformation<Alarm> getProducedType() {
        return TypeInformation.of(Alarm.class);
    }
}

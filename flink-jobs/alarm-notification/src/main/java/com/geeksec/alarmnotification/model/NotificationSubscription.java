package com.geeksec.alarmnotification.model;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 通知订阅模型
 * 只包含流处理必需的字段
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class NotificationSubscription implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 订阅ID
     */
    private String subscriptionId;
    
    /**
     * 是否启用
     */
    private Boolean enabled;
    
    /**
     * 匹配规则列表
     */
    private List<SubscriptionRule> rules;
    
    /**
     * 通知渠道列表
     */
    private List<NotificationChannel> channels;
}
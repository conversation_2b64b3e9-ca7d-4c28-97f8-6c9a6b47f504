package com.geeksec.alarm.processor.config;

import com.geeksec.common.config.ConfigurationManager;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.java.utils.ParameterTool;

import java.io.Serializable;

/**
 * 告警处理器配置类
 * 统一管理所有配置参数
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Data
@Slf4j
public class AlarmProcessorConfig implements Serializable {

        private static final long serialVersionUID = 1L;

        // ==================== 作业基础配置 ====================

        /** 作业名称 */
        private String jobName = "alarm-processor";

        /** 作业并行度 */
        private int jobParallelism = 4;

        /** 检查点间隔（毫秒） */
        private long checkpointInterval = 60000L;

        /** 重启尝试次数 */
        private int restartAttempts = 3;

        /** 重启延迟（毫秒） */
        private long restartDelay = 10000L;

        // ==================== Kafka 配置 ====================

        /** Kafka Bootstrap Servers */
        private String kafkaBootstrapServers = "kafka:9092";

        /** Consumer Group ID */
        private String kafkaGroupId = "alarm-processor";

        /** 输入主题 */
        private String inputTopic = "alarm-events";

        /** 输出主题 */
        private String outputTopic = "processed-alarms";



        /** 起始偏移量 */
        private String startingOffsets = "latest";

        /** 自动提交 */
        private boolean autoCommit = true;

        /** 提交间隔（毫秒） */
        private long commitInterval = 5000L;

        // ==================== 处理配置 ====================

        /** 是否启用去重 */
        private boolean deduplicationEnabled = true;

        /** 去重模式 */
        private String deduplicationMode = "TIME_WINDOW";

        /** 时间窗口大小（毫秒） */
        private long timeWindowMs = 60000L;

        /** 去重缓存最大大小 */
        private int dedupMaxCacheSize = 10000;

        /** 缓存过期时间（毫秒） */
        private long cacheExpirationMs = 300000L;

        /** 是否启用格式化 */
        private boolean formattingEnabled = true;

        /** 是否包含原因分析 */
        private boolean includeReasonAnalysis = true;

        /** 是否包含处理建议 */
        private boolean includeHandlingSuggestions = true;

        /** 是否启用攻击链分析 */
        private boolean attackChainEnabled = true;

        /** 攻击链关联窗口（毫秒） */
        private long correlationWindowMs = 300000L;

        /** 攻击链缓存最大大小 */
        private int attackChainMaxCacheSize = 15000;

        /** 形成攻击链的最小事件数 */
        private int minEventsForChain = 2;

        /** 是否启用批量处理 */
        private boolean batchEnabled = true;

        /** 最大批量大小 */
        private int maxBatchSize = 50;

        /** 最大等待时间（毫秒） */
        private long maxWaitTimeMs = 30000L;

        /** 检查间隔（毫秒） */
        private long checkIntervalMs = 5000L;

        // ==================== 输出配置 ====================

        /** PostgreSQL 主机 */
        private String postgresqlHost = "postgresql";

        /** PostgreSQL 端口 */
        private int postgresqlPort = 5432;

        /** PostgreSQL 数据库名 */
        private String postgresqlDatabase = "nta";

        /** PostgreSQL 用户名 */
        private String postgresqlUsername = "nta_user";

        /** PostgreSQL 密码 */
        private String postgresqlPassword = "nta_password";



        // ==================== 监控配置 ====================

        /** 是否启用监控 */
        private boolean monitoringEnabled = true;

        /** 指标间隔（毫秒） */
        private long metricsInterval = 30000L;

        /** 是否启用性能日志 */
        private boolean performanceLogging = true;

        /** 是否启用详细指标 */
        private boolean detailedMetrics = false;

        // ==================== 白名单配置 ====================

        /** 是否启用白名单 */
        private boolean whitelistEnabled = false;

        /** 白名单配置路径 */
        private String whitelistConfigPath = "alarm_whitelist.json";

        /** 白名单更新间隔（秒） */
        private int whitelistUpdateInterval = 300;

        // ==================== 并行度配置 ====================

        /** Kafka Source 并行度 */
        private int kafkaSourceParallelism = 4;

        /** 处理并行度 */
        private int processingParallelism = 8;

        /** PostgreSQL Sink 并行度 */
        private int postgresqlSinkParallelism = 2;

        /**
         * 从 ParameterTool 创建配置对象
         */
        public static AlarmProcessorConfig fromParameterTool(ParameterTool parameterTool) {
                AlarmProcessorConfig config = new AlarmProcessorConfig();

                // 作业基础配置
                config.setJobName(parameterTool.get("alarm.processor.job.name", config.getJobName()));
                config.setJobParallelism(
                                parameterTool.getInt("alarm.processor.job.parallelism", config.getJobParallelism()));
                config.setCheckpointInterval(parameterTool.getLong("alarm.processor.job.checkpointInterval",
                                config.getCheckpointInterval()));
                config.setRestartAttempts(parameterTool.getInt("alarm.processor.job.restartAttempts",
                                config.getRestartAttempts()));
                config.setRestartDelay(
                                parameterTool.getLong("alarm.processor.job.restartDelay", config.getRestartDelay()));

                // Kafka 配置 - 支持统一配置和专用配置
                config.setKafkaBootstrapServers(parameterTool.get("alarm.processor.kafka.bootstrapServers",
                                parameterTool.get("kafka.bootstrap.servers", config.getKafkaBootstrapServers())));
                config.setKafkaGroupId(parameterTool.get("alarm.processor.kafka.groupId",
                                parameterTool.get("kafka.group.id", config.getKafkaGroupId())));
                config.setInputTopic(parameterTool.get("alarm.processor.kafka.input.topic", config.getInputTopic()));
                config.setOutputTopic(parameterTool.get("alarm.processor.kafka.output.topic", config.getOutputTopic()));

                config.setStartingOffsets(parameterTool.get("alarm.processor.kafka.input.startingOffsets",
                                config.getStartingOffsets()));
                config.setAutoCommit(parameterTool.getBoolean("alarm.processor.kafka.input.autoCommit",
                                config.isAutoCommit()));
                config.setCommitInterval(parameterTool.getLong("alarm.processor.kafka.input.commitInterval",
                                config.getCommitInterval()));

                // 处理配置
                config.setDeduplicationEnabled(parameterTool.getBoolean(
                                "alarm.processor.processing.deduplication.enabled", config.isDeduplicationEnabled()));
                config.setDeduplicationMode(parameterTool.get("alarm.processor.processing.deduplication.mode",
                                config.getDeduplicationMode()));
                config.setTimeWindowMs(parameterTool.getLong("alarm.processor.processing.deduplication.timeWindowMs",
                                config.getTimeWindowMs()));
                config.setDedupMaxCacheSize(
                                parameterTool.getInt("alarm.processor.processing.deduplication.maxCacheSize",
                                                config.getDedupMaxCacheSize()));
                config.setCacheExpirationMs(
                                parameterTool.getLong("alarm.processor.processing.deduplication.cacheExpirationMs",
                                                config.getCacheExpirationMs()));

                config.setFormattingEnabled(parameterTool.getBoolean("alarm.processor.processing.formatting.enabled",
                                config.isFormattingEnabled()));
                config.setIncludeReasonAnalysis(
                                parameterTool.getBoolean("alarm.processor.processing.formatting.includeReasonAnalysis",
                                                config.isIncludeReasonAnalysis()));
                config.setIncludeHandlingSuggestions(parameterTool.getBoolean(
                                "alarm.processor.processing.formatting.includeHandlingSuggestions",
                                config.isIncludeHandlingSuggestions()));

                config.setAttackChainEnabled(parameterTool.getBoolean("alarm.processor.processing.attackChain.enabled",
                                config.isAttackChainEnabled()));
                config.setCorrelationWindowMs(
                                parameterTool.getLong("alarm.processor.processing.attackChain.correlationWindowMs",
                                                config.getCorrelationWindowMs()));
                config.setAttackChainMaxCacheSize(
                                parameterTool.getInt("alarm.processor.processing.attackChain.maxCacheSize",
                                                config.getAttackChainMaxCacheSize()));
                config.setMinEventsForChain(
                                parameterTool.getInt("alarm.processor.processing.attackChain.minEventsForChain",
                                                config.getMinEventsForChain()));

                config.setBatchEnabled(parameterTool.getBoolean("alarm.processor.processing.batch.enabled",
                                config.isBatchEnabled()));
                config.setMaxBatchSize(parameterTool.getInt("alarm.processor.processing.batch.maxBatchSize",
                                config.getMaxBatchSize()));
                config.setMaxWaitTimeMs(parameterTool.getLong("alarm.processor.processing.batch.maxWaitTimeMs",
                                config.getMaxWaitTimeMs()));
                config.setCheckIntervalMs(parameterTool.getLong("alarm.processor.processing.batch.checkIntervalMs",
                                config.getCheckIntervalMs()));

                // 输出配置
                // PostgreSQL 配置 - 支持统一配置和专用配置
                config.setPostgresqlHost(parameterTool.get("alarm.processor.postgresql.host",
                                parameterTool.get("postgresql.host", config.getPostgresqlHost())));
                config.setPostgresqlPort(parameterTool.getInt("alarm.processor.postgresql.port",
                                parameterTool.getInt("postgresql.port", config.getPostgresqlPort())));
                config.setPostgresqlDatabase(parameterTool.get("alarm.processor.postgresql.database",
                                parameterTool.get("postgresql.database", config.getPostgresqlDatabase())));
                config.setPostgresqlUsername(parameterTool.get("alarm.processor.postgresql.username",
                                parameterTool.get("postgresql.username", config.getPostgresqlUsername())));
                config.setPostgresqlPassword(parameterTool.get("alarm.processor.postgresql.password",
                                parameterTool.get("postgresql.password", config.getPostgresqlPassword())));

                // 处理配置
                config.setDeduplicationEnabled(parameterTool.getBoolean(
                                "alarm.processor.processing.deduplication.enabled", config.isDeduplicationEnabled()));
                config.setDeduplicationTimeWindowMs(
                                parameterTool.getLong("alarm.processor.processing.deduplication.timeWindowMs",
                                                config.getDeduplicationTimeWindowMs()));
                config.setDeduplicationMaxCacheSize(
                                parameterTool.getInt("alarm.processor.processing.deduplication.maxCacheSize",
                                                config.getDeduplicationMaxCacheSize()));

                config.setFormattingEnabled(parameterTool.getBoolean("alarm.processor.processing.formatting.enabled",
                                config.isFormattingEnabled()));
                config.setIncludeReasonAnalysis(
                                parameterTool.getBoolean("alarm.processor.processing.formatting.includeReasonAnalysis",
                                                config.isIncludeReasonAnalysis()));
                config.setIncludeHandlingSuggestions(parameterTool.getBoolean(
                                "alarm.processor.processing.formatting.includeHandlingSuggestions",
                                config.isIncludeHandlingSuggestions()));

                config.setAttackChainEnabled(parameterTool.getBoolean("alarm.processor.processing.attackChain.enabled",
                                config.isAttackChainEnabled()));
                config.setAttackChainCorrelationWindowMs(
                                parameterTool.getLong("alarm.processor.processing.attackChain.correlationWindowMs",
                                                config.getAttackChainCorrelationWindowMs()));
                config.setAttackChainMaxCacheSize(
                                parameterTool.getInt("alarm.processor.processing.attackChain.maxCacheSize",
                                                config.getAttackChainMaxCacheSize()));

                config.setBatchEnabled(parameterTool.getBoolean("alarm.processor.processing.batch.enabled",
                                config.isBatchEnabled()));
                config.setBatchMaxBatchSize(parameterTool.getInt("alarm.processor.processing.batch.maxBatchSize",
                                config.getBatchMaxBatchSize()));
                config.setBatchMaxWaitTimeMs(parameterTool.getLong("alarm.processor.processing.batch.maxWaitTimeMs",
                                config.getBatchMaxWaitTimeMs()));





                // 监控配置
                config.setMonitoringEnabled(parameterTool.getBoolean("alarm.processor.monitoring.enabled",
                                config.isMonitoringEnabled()));
                config.setMetricsInterval(parameterTool.getLong("alarm.processor.monitoring.metricsInterval",
                                config.getMetricsInterval()));

                // 并行度配置
                config.setKafkaSourceParallelism(parameterTool.getInt("alarm.processor.parallelism.kafka.source",
                                parameterTool.getInt("parallelism.kafka.source", config.getKafkaSourceParallelism())));
                config.setProcessingParallelism(parameterTool.getInt("alarm.processor.parallelism.processing",
                                parameterTool.getInt("parallelism.processing", config.getProcessingParallelism())));
                config.setPostgresqlSinkParallelism(parameterTool.getInt("alarm.processor.parallelism.postgresql.sink",
                                config.getPostgresqlSinkParallelism()));


                // 监控配置
                config.setMonitoringEnabled(parameterTool.getBoolean("alarm.processor.monitoring.enabled",
                                config.isMonitoringEnabled()));
                config.setMetricsInterval(parameterTool.getLong("alarm.processor.monitoring.metricsInterval",
                                config.getMetricsInterval()));
                config.setPerformanceLogging(parameterTool.getBoolean("alarm.processor.monitoring.performanceLogging",
                                config.isPerformanceLogging()));
                config.setDetailedMetrics(parameterTool.getBoolean("alarm.processor.monitoring.detailedMetrics",
                                config.isDetailedMetrics()));

                // 白名单配置
                config.setWhitelistEnabled(parameterTool.getBoolean("alarm.processor.whitelist.enabled",
                                config.isWhitelistEnabled()));
                config.setWhitelistConfigPath(parameterTool.get("alarm.processor.whitelist.configPath",
                                config.getWhitelistConfigPath()));
                config.setWhitelistUpdateInterval(parameterTool.getInt("alarm.processor.whitelist.updateInterval",
                                config.getWhitelistUpdateInterval()));

                log.info("告警处理器配置加载完成");
                return config;
        }

        /**
         * 打印配置信息
         */
        public void printConfig() {
                log.info("=== 告警处理器配置信息 ===");
                log.info("作业名称: {}", jobName);
                log.info("作业并行度: {}", jobParallelism);
                log.info("检查点间隔: {}ms", checkpointInterval);
                log.info("Kafka Bootstrap Servers: {}", kafkaBootstrapServers);
                log.info("输入主题: {}", inputTopic);
                log.info("输出主题: {}", outputTopic);
                log.info("去重启用: {}", deduplicationEnabled);
                log.info("格式化启用: {}", formattingEnabled);
                log.info("攻击链分析启用: {}", attackChainEnabled);
                log.info("PostgreSQL 输出: {}:{}/{}", postgresqlHost, postgresqlPort, postgresqlDatabase);

                log.info("监控启用: {}", monitoringEnabled);
                log.info("========================");
        }


}

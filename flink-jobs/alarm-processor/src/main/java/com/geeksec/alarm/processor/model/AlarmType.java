package com.geeksec.alarm.processor.model;

/**
 * 告警类型枚举
 *
 * <AUTHOR>
 * @since 3.0.0
 */
public enum AlarmType {

    // 挖矿相关
    MINING_VIRUS("挖矿病毒", "ACTIONS_ON_OBJECTIVES", "检测到挖矿病毒活动"),
    MINING_CONNECTION_ATTEMPT("尝试挖矿连接", "COMMAND_AND_CONTROL", "检测到尝试连接挖矿池的行为"),

    // 扫描行为
    SCAN_BEHAVIOR("扫描行为", "RECONNAISSANCE", "检测到网络扫描行为"),
    PENETRATION_TOOL_FINGERPRINT("渗透工具指纹", "RECONNAISSANCE", "检测到渗透工具指纹"),
    PORT_SCAN_SIP("端口扫描sip", "RECONNAISSANCE", "检测到源IP端口扫描"),
    PORT_SCAN_DIP("端口扫描dip", "RECONNAISSANCE", "检测到目标IP端口扫描"),
    WEB_LOGIN_BRUTE_FORCE("web登录爆破", "EXPLOITATION", "检测到Web登录暴力破解"),

    // 远控木马
    REMOTE_TROJAN("远控木马", "INSTALLATION", "检测到远程控制木马"),
    ILLEGAL_EXTERNAL_CONNECTION("违规外联", "ACTIONS_ON_OBJECTIVES", "检测到违规外联行为"),

    // 指纹随机化
    FINGERPRINT_RANDOMIZATION("指纹随机化访问服务端", "DELIVERY", "检测到指纹随机化访问"),

    // 隐蔽隧道
    COVERT_TUNNEL("隐蔽隧道", "COMMAND_AND_CONTROL", "检测到隐蔽隧道通信"),
    DNS_TUNNEL("DNS隐蔽隧道", "COMMAND_AND_CONTROL", "检测到DNS隐蔽隧道"),
    TCP_TUNNEL("TCP隐蔽隧道", "COMMAND_AND_CONTROL", "检测到TCP隐蔽隧道"),
    HTTP_TUNNEL("HTTP隐蔽隧道", "COMMAND_AND_CONTROL", "检测到HTTP隐蔽隧道"),
    NTP_TUNNEL("NTP隐蔽隧道", "COMMAND_AND_CONTROL", "检测到NTP隐蔽隧道"),
    SSL_TUNNEL("SSL隐蔽隧道", "COMMAND_AND_CONTROL", "检测到SSL隐蔽隧道"),
    ICMP_TUNNEL("ICMP隐蔽隧道", "COMMAND_AND_CONTROL", "检测到ICMP隐蔽隧道"),

    // WebShell
    WEBSHELL_ATTACK("webShell攻击检测", "INSTALLATION", "检测到WebShell攻击"),

    // 特定协议攻击工具
    SPECIFIC_PROTOCOL_ATTACK_TOOL("特定协议攻击工具", "WEAPONIZATION", "检测到特定协议攻击工具"),

    // C2行为
    STANDARD_REMOTE_CONTROL_C2("标准远程控制协议下的C2行为", "COMMAND_AND_CONTROL", "检测到标准远程控制协议下的C2行为"),
    UNKNOWN_REMOTE_CONTROL_PROTOCOL("未知远程控制协议", "COMMAND_AND_CONTROL", "检测到未知远程控制协议"),

    // 证书相关
    CERTIFICATE_ANOMALY("证书异常", "DELIVERY", "检测到证书异常"),
    CERTIFICATE_COLLISION("证书碰撞", "DELIVERY", "检测到证书哈希碰撞"),
    BLOCKED_CERTIFICATE("黑名单证书", "DELIVERY", "检测到黑名单证书"),

    // 通用类型
    UNKNOWN("未知", "OTHER", "未知告警类型");

    private final String displayName;
    private final String killChainStage;
    private final String description;

    AlarmType(String displayName, String killChainStage, String description) {
        this.displayName = displayName;
        this.killChainStage = killChainStage;
        this.description = description;
    }

    public String getDisplayName() {
        return displayName;
    }

    /**
     * 获取 Cyber Kill Chain 阶段
     *
     * @return Cyber Kill Chain 阶段名称
     */
    public String getKillChainStage() {
        return killChainStage;
    }

    public String getDescription() {
        return description;
    }
    
    /**
     * 根据告警类型名称获取枚举
     */
    public static AlarmType fromDisplayName(String displayName) {
        if (displayName == null) {
            return UNKNOWN;
        }
        
        for (AlarmType type : values()) {
            if (type.displayName.equals(displayName)) {
                return type;
            }
        }
        
        return UNKNOWN;
    }
    
    /**
     * 检查是否为挖矿相关告警
     */
    public boolean isMiningRelated() {
        return this == MINING_VIRUS || this == MINING_CONNECTION_ATTEMPT;
    }
    
    /**
     * 检查是否为扫描相关告警
     */
    public boolean isScanRelated() {
        return this == SCAN_BEHAVIOR || this == PENETRATION_TOOL_FINGERPRINT || 
               this == PORT_SCAN_SIP || this == PORT_SCAN_DIP || this == WEB_LOGIN_BRUTE_FORCE;
    }
    
    /**
     * 检查是否为隧道相关告警
     */
    public boolean isTunnelRelated() {
        return this == COVERT_TUNNEL || this == DNS_TUNNEL || this == TCP_TUNNEL || 
               this == HTTP_TUNNEL || this == NTP_TUNNEL || this == SSL_TUNNEL || this == ICMP_TUNNEL;
    }
    
    /**
     * 检查是否为C2相关告警
     */
    public boolean isC2Related() {
        return this == REMOTE_TROJAN || this == STANDARD_REMOTE_CONTROL_C2 || 
               this == UNKNOWN_REMOTE_CONTROL_PROTOCOL || isTunnelRelated();
    }
    
    /**
     * 检查是否为证书相关告警
     */
    public boolean isCertificateRelated() {
        return this == CERTIFICATE_ANOMALY || this == CERTIFICATE_COLLISION || this == BLOCKED_CERTIFICATE;
    }
    
    /**
     * 获取告警优先级
     */
    public int getPriority() {
        switch (this) {
            case MINING_VIRUS:
            case WEBSHELL_ATTACK:
            case REMOTE_TROJAN:
                return 4; // 严重
            case MINING_CONNECTION_ATTEMPT:
            case COVERT_TUNNEL:
            case DNS_TUNNEL:
            case STANDARD_REMOTE_CONTROL_C2:
                return 3; // 高
            case SCAN_BEHAVIOR:
            case PENETRATION_TOOL_FINGERPRINT:
            case ILLEGAL_EXTERNAL_CONNECTION:
                return 2; // 中
            default:
                return 1; // 低
        }
    }
}

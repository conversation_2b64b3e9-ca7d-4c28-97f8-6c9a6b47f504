package com.geeksec.alarm.processor.pipeline.function;

import com.geeksec.alarm.processor.config.AlarmProcessorConfig;
import com.geeksec.alarm.processor.model.Alarm;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.common.functions.RichMapFunction;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.metrics.Counter;
import org.apache.flink.metrics.MetricGroup;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 告警格式化功能
 * 对已经过知识库增强和类型特化处理的告警进行最终格式化
 *
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
public class FormattingFunction extends RichMapFunction<Alarm, Alarm> {
    
    private static final long serialVersionUID = 1L;
    
    private final AlarmProcessorConfig config;
    
    /** 指标计数器 */
    private transient Counter totalAlarms;
    private transient Counter formattedAlarms;
    private transient Counter formattingErrors;
    
    public FormattingFunction(AlarmProcessorConfig config) {
        this.config = config;
    }
    
    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        
        // 初始化指标
        MetricGroup metricGroup = getRuntimeContext().getMetricGroup()
                .addGroup("alarm-formatting");
        
        totalAlarms = metricGroup.counter("total_alarms");
        formattedAlarms = metricGroup.counter("formatted_alarms");
        formattingErrors = metricGroup.counter("formatting_errors");
        
        log.info("告警格式化功能初始化完成");
    }
    
    @Override
    public Alarm map(Alarm alarm) throws Exception {
        totalAlarms.inc();
        
        try {
            // 检查是否已经过知识库增强和类型特化处理
            if (alarm.getProcessingStatus().getIsKnowledgeEnriched() != null &&
                alarm.getProcessingStatus().getIsKnowledgeEnriched()) {
                // 已经过特化处理，只需要进行最终格式化
                performFinalFormatting(alarm);
            } else {
                // 未经过特化处理，使用通用格式化（兼容模式）
                performGenericFormatting(alarm);
            }

            // 标记为已格式化
            alarm.getProcessingStatus().setIsFormatted(true);

            formattedAlarms.inc();
            log.debug("告警格式化完成: {}", alarm.getAlarmId());

            return alarm;
            
        } catch (Exception e) {
            formattingErrors.inc();
            log.error("告警格式化失败: {}", e.getMessage(), e);
            
            // 添加错误信息到处理状态
            if (alarm.getProcessingStatus().getProcessingErrors() == null) {
                alarm.getProcessingStatus().setProcessingErrors(new ArrayList<>());
            }
            alarm.getProcessingStatus().getProcessingErrors().add("格式化失败: " + e.getMessage());
            
            return alarm;
        }
    }

    /**
     * 执行最终格式化（针对已经过特化处理的告警）
     */
    private void performFinalFormatting(Alarm alarm) {
        // 如果原因分析为空，生成基础原因分析
        if (alarm.getReasonAnalysis() == null) {
            alarm.setReasonAnalysis(generateBasicReasonAnalysis(alarm));
        } else {
            // 计算风险评分
            int riskScore = calculateRiskScore(alarm, alarm.getReasonAnalysis().getDetailedReasons());
            alarm.getReasonAnalysis().setRiskScore(riskScore);

            // 生成证据列表
            List<String> evidence = generateEvidence(alarm);
            alarm.getReasonAnalysis().setEvidence(evidence);
        }

        // 如果处理建议为空，生成基础处理建议
        if (alarm.getHandlingSuggestions() == null) {
            alarm.setHandlingSuggestions(generateBasicHandlingSuggestions(alarm));
        }
    }

    /**
     * 执行通用格式化（兼容模式）
     */
    private void performGenericFormatting(Alarm alarm) {
        // 生成原因分析
        if (config.isIncludeReasonAnalysis()) {
            Alarm.ReasonAnalysis reasonAnalysis = generateReasonAnalysis(alarm);
            alarm.setReasonAnalysis(reasonAnalysis);
        }

        // 生成处理建议
        if (config.isIncludeHandlingSuggestions()) {
            Alarm.HandlingSuggestions handlingSuggestions = generateHandlingSuggestions(alarm);
            alarm.setHandlingSuggestions(handlingSuggestions);
        }
    }

    /**
     * 生成基础原因分析
     */
    private Alarm.ReasonAnalysis generateBasicReasonAnalysis(Alarm alarm) {
        List<Alarm.DetectionReason> detailedReasons = new ArrayList<>();

        // 生成基础检测原因
        detailedReasons.add(Alarm.DetectionReason.builder()
                .category("异常行为")
                .description("检测到可疑的网络行为模式")
                .expectedValue("正常行为模式")
                .actualValue(alarm.getDescription() != null ? alarm.getDescription() : "未知异常")
                .normalValue("正常行为模式")
                .severity(5)
                .build());

        // 计算风险评分
        int riskScore = calculateRiskScore(alarm, detailedReasons);

        // 生成证据列表
        List<String> evidence = generateEvidence(alarm);

        return Alarm.ReasonAnalysis.builder()
                .primaryReason(detailedReasons.isEmpty() ? "未知原因" : detailedReasons.get(0).getDescription())
                .detailedReasons(detailedReasons)
                .riskScore(riskScore)
                .evidence(evidence)
                .build();
    }

    /**
     * 生成基础处理建议
     */
    private Alarm.HandlingSuggestions generateBasicHandlingSuggestions(Alarm alarm) {
        return Alarm.HandlingSuggestions.builder()
                .immediateActions(Arrays.asList(
                        "分析告警详情，确认威胁性质",
                        "检查相关系统和网络状态",
                        "必要时隔离可疑主机或网络连接"
                ))
                .investigationSteps(Arrays.asList(
                        "收集相关日志和证据",
                        "分析攻击模式和影响范围",
                        "确认是否为误报",
                        "评估安全风险等级"
                ))
                .preventiveMeasures(Arrays.asList(
                        "更新安全策略和规则",
                        "加强网络监控",
                        "定期进行安全评估",
                        "提升安全防护能力"
                ))
                .recoverySteps(Arrays.asList(
                        "修复发现的安全问题",
                        "恢复受影响的服务",
                        "更新安全配置",
                        "持续监控系统状态"
                ))
                .build();
    }

    /**
     * 生成原因分析（兼容模式）
     */
    private Alarm.ReasonAnalysis generateReasonAnalysis(Alarm alarm) {
        List<Alarm.DetectionReason> detailedReasons = new ArrayList<>();
        
        // 根据告警类型生成检测原因
        switch (alarm.getAlarmType()) {
            case "APT证书碰撞":
            case "威胁证书碰撞":
                detailedReasons.add(createDetectionReason(
                        "知识库碰撞",
                        "证书与威胁情报知识库中的已知威胁证书匹配",
                        "证书哈希",
                        alarm.getCertificateInfo() != null ? alarm.getCertificateInfo().getCertHash() : "未知",
                        "正常证书哈希",
                        9
                ));
                break;
                
            case "恶意域名关联证书":
                detailedReasons.add(createDetectionReason(
                        "恶意域名关联",
                        "证书包含已知的恶意域名",
                        "域名列表",
                        alarm.getCertificateInfo() != null ? String.join(",", alarm.getCertificateInfo().getDomains()) : "未知",
                        "合法域名",
                        8
                ));
                break;
                
            case "C2证书请求":
                detailedReasons.add(createDetectionReason(
                        "C2通信特征",
                        "检测到与C2服务器通信的证书特征",
                        "通信模式",
                        "疑似C2通信",
                        "正常HTTPS通信",
                        9
                ));
                break;
                
            case "非法挖矿请求":
                detailedReasons.add(createDetectionReason(
                        "挖矿行为特征",
                        "检测到与挖矿活动相关的证书使用",
                        "行为模式",
                        "挖矿连接特征",
                        "正常网络连接",
                        7
                ));
                break;
                
            default:
                detailedReasons.add(createDetectionReason(
                        "异常行为",
                        "检测到可疑的网络行为模式",
                        "行为特征",
                        alarm.getDescription() != null ? alarm.getDescription() : "未知异常",
                        "正常行为模式",
                        5
                ));
        }
        
        // 计算风险评分
        int riskScore = calculateRiskScore(alarm, detailedReasons);
        
        // 生成证据列表
        List<String> evidence = generateEvidence(alarm);
        
        return Alarm.ReasonAnalysis.builder()
                .primaryReason(detailedReasons.isEmpty() ? "未知原因" : detailedReasons.get(0).getDescription())
                .detailedReasons(detailedReasons)
                .riskScore(riskScore)
                .evidence(evidence)
                .build();
    }
    
    /**
     * 创建检测原因
     */
    private Alarm.DetectionReason createDetectionReason(String reasonType, String description,
                                                       String detectedFeature, String actualValue,
                                                       String expectedValue, int importance) {
        return Alarm.DetectionReason.builder()
                .reasonType(reasonType)
                .description(description)
                .detectedFeature(detectedFeature)
                .actualValue(actualValue)
                .expectedValue(expectedValue)
                .importance(importance)
                .build();
    }
    
    /**
     * 计算风险评分
     */
    private int calculateRiskScore(Alarm alarm, List<Alarm.DetectionReason> reasons) {
        int baseScore = 0;
        
        // 根据告警级别设置基础分数
        switch (alarm.getAlarmLevel()) {
            case LOW:
                baseScore = 30;
                break;
            case MEDIUM:
                baseScore = 50;
                break;
            case HIGH:
                baseScore = 70;
                break;
            case CRITICAL:
                baseScore = 90;
                break;
        }
        
        // 根据置信度调整分数
        if (alarm.getConfidence() != null) {
            baseScore = (int) (baseScore * alarm.getConfidence());
        }
        
        // 根据检测原因的重要性调整分数
        int reasonScore = reasons.stream()
                .mapToInt(Alarm.DetectionReason::getImportance)
                .max()
                .orElse(0);
        
        int finalScore = Math.max(baseScore, reasonScore * 10);
        return Math.min(finalScore, 100); // 确保不超过100
    }
    
    /**
     * 生成证据列表
     */
    private List<String> generateEvidence(Alarm alarm) {
        List<String> evidence = new ArrayList<>();
        
        if (alarm.getSrcIp() != null) {
            evidence.add("源IP地址: " + alarm.getSrcIp());
        }
        
        if (alarm.getDstIp() != null) {
            evidence.add("目标IP地址: " + alarm.getDstIp());
        }
        
        if (alarm.getProtocol() != null) {
            evidence.add("协议类型: " + alarm.getProtocol());
        }
        
        if (alarm.getCertificateInfo() != null) {
            evidence.add("证书哈希: " + alarm.getCertificateInfo().getCertHash());
            if (alarm.getCertificateInfo().getSubjectCn() != null) {
                evidence.add("证书主题: " + alarm.getCertificateInfo().getSubjectCn());
            }
        }
        
        if (alarm.getDetectorType() != null) {
            evidence.add("检测器类型: " + alarm.getDetectorType());
        }
        
        return evidence;
    }
    
    /**
     * 生成处理建议
     */
    private Alarm.HandlingSuggestions generateHandlingSuggestions(Alarm alarm) {
        List<String> immediateActions = new ArrayList<>();
        List<String> investigationSteps = new ArrayList<>();
        List<String> preventionMeasures = new ArrayList<>();
        String priority;
        
        // 根据告警类型和级别生成建议
        if (alarm.isHighPriority()) {
            priority = "高";
            immediateActions.addAll(Arrays.asList(
                    "立即隔离相关主机或网络段",
                    "阻断可疑IP地址的网络连接",
                    "通知安全团队进行紧急响应"
            ));
        } else {
            priority = "中";
            immediateActions.addAll(Arrays.asList(
                    "监控相关网络活动",
                    "收集更多证据信息",
                    "评估影响范围"
            ));
        }
        
        // 通用调查步骤
        investigationSteps.addAll(Arrays.asList(
                "分析相关日志文件",
                "检查网络流量模式",
                "验证告警的准确性",
                "确定攻击时间线",
                "评估数据泄露风险"
        ));
        
        // 预防措施
        preventionMeasures.addAll(Arrays.asList(
                "更新安全策略和规则",
                "加强网络监控",
                "定期更新威胁情报",
                "进行安全意识培训",
                "完善应急响应流程"
        ));
        
        return Alarm.HandlingSuggestions.builder()
                .immediateActions(immediateActions)
                .investigationSteps(investigationSteps)
                .preventionMeasures(preventionMeasures)
                .priority(priority)
                .build();
    }
    
    @Override
    public void close() throws Exception {
        log.info("告警格式化功能关闭，格式化统计: 总计={}, 成功={}, 失败={}", 
                totalAlarms.getCount(), 
                formattedAlarms.getCount(), 
                formattingErrors.getCount());
        super.close();
    }
}

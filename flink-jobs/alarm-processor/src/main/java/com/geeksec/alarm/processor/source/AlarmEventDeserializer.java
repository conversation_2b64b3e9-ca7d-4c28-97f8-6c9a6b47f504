package com.geeksec.alarm.processor.source;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.geeksec.alarm.processor.model.AlarmEvent;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.common.functions.RichFlatMapFunction;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.metrics.Counter;
import org.apache.flink.metrics.MetricGroup;
import org.apache.flink.util.Collector;

/**
 * 告警事件反序列化器
 * 将 JSON 字符串转换为 AlarmEvent 对象
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
public class AlarmEventDeserializer extends RichFlatMapFunction<String, AlarmEvent> {
    
    private static final long serialVersionUID = 1L;
    
    private final ObjectMapper objectMapper;
    
    // 指标计数器
    private transient Counter totalRecords;
    private transient Counter successfulRecords;
    private transient Counter failedRecords;
    private transient Counter emptyRecords;
    
    public AlarmEventDeserializer(ObjectMapper objectMapper) {
        this.objectMapper = objectMapper;
    }
    
    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        
        // 初始化指标
        MetricGroup metricGroup = getRuntimeContext().getMetricGroup()
                .addGroup("alarm-event-deserializer");
        
        totalRecords = metricGroup.counter("total_records");
        successfulRecords = metricGroup.counter("successful_records");
        failedRecords = metricGroup.counter("failed_records");
        emptyRecords = metricGroup.counter("empty_records");
        
        log.info("告警事件反序列化器初始化完成");
    }
    
    @Override
    public void flatMap(String value, Collector<AlarmEvent> out) throws Exception {
        totalRecords.inc();
        
        // 检查空值或空字符串
        if (value == null || value.trim().isEmpty()) {
            emptyRecords.inc();
            log.debug("接收到空的告警事件消息");
            return;
        }
        
        try {
            // 反序列化 JSON 为 AlarmEvent 对象
            AlarmEvent alarmEvent = objectMapper.readValue(value, AlarmEvent.class);
            
            // 基础验证
            if (isValidAlarmEvent(alarmEvent)) {
                successfulRecords.inc();
                out.collect(alarmEvent);
                log.debug("成功反序列化告警事件: {}", alarmEvent.getEventId());
            } else {
                failedRecords.inc();
                log.warn("告警事件验证失败，跳过处理: {}", value);
            }
            
        } catch (Exception e) {
            failedRecords.inc();
            log.error("反序列化告警事件失败: {}, 原始数据: {}", e.getMessage(), value, e);
        }
    }
    
    /**
     * 基础验证告警事件
     */
    private boolean isValidAlarmEvent(AlarmEvent alarmEvent) {
        if (alarmEvent == null) {
            log.debug("告警事件为空");
            return false;
        }
        
        if (alarmEvent.getEventId() == null || alarmEvent.getEventId().trim().isEmpty()) {
            log.debug("告警事件ID为空");
            return false;
        }
        
        if (alarmEvent.getSourceModule() == null || alarmEvent.getSourceModule().trim().isEmpty()) {
            log.debug("告警事件来源模块为空");
            return false;
        }
        
        if (alarmEvent.getAlarmType() == null || alarmEvent.getAlarmType().trim().isEmpty()) {
            log.debug("告警类型为空");
            return false;
        }
        
        if (alarmEvent.getTimestamp() == null) {
            log.debug("告警时间戳为空");
            return false;
        }
        
        return true;
    }
    
    @Override
    public void close() throws Exception {
        log.info("告警事件反序列化器关闭，处理统计: 总计={}, 成功={}, 失败={}, 空记录={}", 
                totalRecords.getCount(), 
                successfulRecords.getCount(), 
                failedRecords.getCount(), 
                emptyRecords.getCount());
        super.close();
    }
}

package com.geeksec.alarm.processor.whitelist;

import com.geeksec.alarm.processor.config.AlarmProcessorConfig;
import com.geeksec.alarm.processor.model.Alarm;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import lombok.extern.slf4j.Slf4j;

import java.sql.*;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 基于PostgreSQL的白名单服务实现
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
public class PostgreSQLWhitelistService implements WhitelistService {
    
    private static final long serialVersionUID = 1L;
    
    private final AlarmProcessorConfig config;
    
    /** 白名单缓存 */
    private final Cache<String, Boolean> whitelistCache;
    
    /** 缓存过期时间（分钟） */
    private static final int CACHE_EXPIRE_MINUTES = 30;
    
    /** 缓存最大大小 */
    private static final int CACHE_MAX_SIZE = 50000;
    
    public PostgreSQLWhitelistService(AlarmProcessorConfig config) {
        this.config = config;
        this.whitelistCache = Caffeine.newBuilder()
                .maximumSize(CACHE_MAX_SIZE)
                .expireAfterWrite(CACHE_EXPIRE_MINUTES, TimeUnit.MINUTES)
                .recordStats()
                .build();
        
        log.info("PostgreSQL白名单服务初始化完成");
    }
    
    @Override
    public boolean isInWhitelist(Alarm alarm) {
        try {
            // 检查IP白名单
            if (isIpInWhitelist(alarm.getSrcIp(), getTaskId(alarm)) || 
                isIpInWhitelist(alarm.getDstIp(), getTaskId(alarm))) {
                log.debug("告警 {} 的IP在白名单中", alarm.getAlarmId());
                return true;
            }
            
            // 检查域名白名单
            String domain = extractDomain(alarm);
            if (domain != null && isDomainInWhitelist(domain, getTaskId(alarm))) {
                log.debug("告警 {} 的域名在白名单中", alarm.getAlarmId());
                return true;
            }
            
            // 检查证书白名单
            String certSha1 = extractCertificateSha1(alarm);
            if (certSha1 != null && isCertificateInWhitelist(certSha1, getTaskId(alarm))) {
                log.debug("告警 {} 的证书在白名单中", alarm.getAlarmId());
                return true;
            }
            
            // 检查告警白名单
            if (isAlarmInWhitelist(alarm.getSrcIp(), alarm.getDstIp(), alarm.getAlarmType())) {
                log.debug("告警 {} 在告警白名单中", alarm.getAlarmId());
                return true;
            }
            
            // 检查攻击链白名单
            List<String> attackChain = extractAttackChain(alarm);
            if (attackChain != null && isAttackChainInWhitelist(attackChain)) {
                log.debug("告警 {} 的攻击链在白名单中", alarm.getAlarmId());
                return true;
            }
            
            return false;
            
        } catch (Exception e) {
            log.error("检查白名单时发生异常: {}", e.getMessage(), e);
            // 异常时不过滤告警，避免漏报
            return false;
        }
    }
    
    @Override
    public boolean isIpInWhitelist(String ip, Integer taskId) {
        if (ip == null || ip.trim().isEmpty()) {
            return false;
        }
        
        String cacheKey = String.format("ip:%s:%d", ip, taskId != null ? taskId : 0);
        return whitelistCache.get(cacheKey, key -> checkIpInDatabase(ip, taskId));
    }
    
    @Override
    public boolean isDomainInWhitelist(String domain, Integer taskId) {
        if (domain == null || domain.trim().isEmpty()) {
            return false;
        }
        
        String cacheKey = String.format("domain:%s:%d", domain, taskId != null ? taskId : 0);
        return whitelistCache.get(cacheKey, key -> checkDomainInDatabase(domain, taskId));
    }
    
    @Override
    public boolean isCertificateInWhitelist(String certSha1, Integer taskId) {
        if (certSha1 == null || certSha1.trim().isEmpty()) {
            return false;
        }
        
        String cacheKey = String.format("cert:%s:%d", certSha1, taskId != null ? taskId : 0);
        return whitelistCache.get(cacheKey, key -> checkCertificateInDatabase(certSha1, taskId));
    }
    
    @Override
    public boolean isAlarmInWhitelist(String victim, String attacker, String label) {
        if (victim == null || attacker == null || label == null) {
            return false;
        }
        
        String cacheKey = String.format("alarm:%s:%s:%s", victim, attacker, label);
        return whitelistCache.get(cacheKey, key -> checkAlarmInDatabase(victim, attacker, label));
    }
    
    @Override
    public boolean isAttackChainInWhitelist(List<String> attackChainList) {
        if (attackChainList == null || attackChainList.isEmpty()) {
            return false;
        }
        
        // 生成攻击链的唯一标识
        String attackChainKey = String.join("-", attackChainList);
        String cacheKey = String.format("chain:%s", attackChainKey);
        
        return whitelistCache.get(cacheKey, key -> {
            // 检查每个攻击链元素
            for (String chainElement : attackChainList) {
                String[] parts = chainElement.split("_");
                if (parts.length >= 3) {
                    String victim = parts[0];
                    String attacker = parts[1];
                    String label = parts[2];
                    
                    if (isAlarmInWhitelist(victim, attacker, label)) {
                        return true;
                    }
                }
            }
            return false;
        });
    }
    
    @Override
    public void refreshWhitelistCache() {
        log.info("刷新白名单缓存");
        whitelistCache.invalidateAll();
        log.info("白名单缓存已刷新");
    }
    
    @Override
    public WhitelistStatistics getWhitelistStatistics() {
        WhitelistStatistics stats = new WhitelistStatistics();
        
        try (Connection conn = createConnection()) {
            // 统计IP白名单
            stats.setIpWhitelistCount(countRecords(conn, "internal_ip_whitelist"));
            
            // 统计域名白名单
            stats.setDomainWhitelistCount(countRecords(conn, "internal_domain_whitelist"));
            
            // 统计证书白名单
            stats.setCertificateWhitelistCount(countRecords(conn, "internal_certificate_whitelist"));
            
            // 统计告警白名单
            stats.setAlarmWhitelistCount(countRecords(conn, "alarm_whitelist"));
            
            stats.setLastUpdateTime(System.currentTimeMillis());
            
        } catch (SQLException e) {
            log.error("获取白名单统计信息失败", e);
        }
        
        return stats;
    }
    
    /**
     * 在数据库中检查IP白名单
     */
    private boolean checkIpInDatabase(String ip, Integer taskId) {
        String sql = "SELECT COUNT(*) FROM internal_ip_whitelist WHERE ip = ? AND (task_id = ? OR task_id = 0)";
        
        try (Connection conn = createConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setString(1, ip);
            stmt.setInt(2, taskId != null ? taskId : 0);
            
            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    return rs.getInt(1) > 0;
                }
            }
            
        } catch (SQLException e) {
            log.error("检查IP白名单失败: {}", ip, e);
        }
        
        return false;
    }
    
    /**
     * 在数据库中检查域名白名单
     */
    private boolean checkDomainInDatabase(String domain, Integer taskId) {
        // 检查精确匹配和N级域名匹配
        String sql = "SELECT COUNT(*) FROM internal_domain_whitelist WHERE " +
                    "(domain_name = ? AND type = 0 AND (task_id = ? OR task_id = 0)) OR " +
                    "(? LIKE '%.' || domain_name AND type = 1 AND (task_id = ? OR task_id = 0))";
        
        try (Connection conn = createConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setString(1, domain);
            stmt.setInt(2, taskId != null ? taskId : 0);
            stmt.setString(3, domain);
            stmt.setInt(4, taskId != null ? taskId : 0);
            
            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    return rs.getInt(1) > 0;
                }
            }
            
        } catch (SQLException e) {
            log.error("检查域名白名单失败: {}", domain, e);
        }
        
        return false;
    }
    
    /**
     * 在数据库中检查证书白名单
     */
    private boolean checkCertificateInDatabase(String certSha1, Integer taskId) {
        String sql = "SELECT COUNT(*) FROM internal_certificate_whitelist WHERE cert_sha1 = ? AND (task_id = ? OR task_id = 0)";
        
        try (Connection conn = createConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setString(1, certSha1);
            stmt.setInt(2, taskId != null ? taskId : 0);
            
            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    return rs.getInt(1) > 0;
                }
            }
            
        } catch (SQLException e) {
            log.error("检查证书白名单失败: {}", certSha1, e);
        }
        
        return false;
    }
    
    /**
     * 在数据库中检查告警白名单
     */
    private boolean checkAlarmInDatabase(String victim, String attacker, String label) {
        String sql = "SELECT COUNT(*) FROM alarm_whitelist WHERE victim = ? AND attacker = ? AND label = ?";
        
        try (Connection conn = createConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setString(1, victim);
            stmt.setString(2, attacker);
            stmt.setString(3, label);
            
            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    return rs.getInt(1) > 0;
                }
            }
            
        } catch (SQLException e) {
            log.error("检查告警白名单失败: victim={}, attacker={}, label={}", victim, attacker, label, e);
        }
        
        return false;
    }
    
    /**
     * 创建数据库连接
     */
    private Connection createConnection() throws SQLException {
        String url = String.format("jdbc:postgresql://%s:%d/%s",
                config.getPostgresqlHost(),
                config.getPostgresqlPort(),
                config.getPostgresqlDatabase());
        
        return DriverManager.getConnection(url,
                config.getPostgresqlUsername(),
                config.getPostgresqlPassword());
    }
    
    /**
     * 统计表记录数
     */
    private long countRecords(Connection conn, String tableName) throws SQLException {
        String sql = "SELECT COUNT(*) FROM " + tableName;
        try (PreparedStatement stmt = conn.prepareStatement(sql);
             ResultSet rs = stmt.executeQuery()) {
            if (rs.next()) {
                return rs.getLong(1);
            }
        }
        return 0;
    }
    
    /**
     * 从告警中提取任务ID
     */
    private Integer getTaskId(Alarm alarm) {
        if (alarm.getExtendedProperties() != null) {
            Object taskId = alarm.getExtendedProperties().get("task_id");
            if (taskId instanceof Integer) {
                return (Integer) taskId;
            } else if (taskId instanceof String) {
                try {
                    return Integer.parseInt((String) taskId);
                } catch (NumberFormatException e) {
                    log.warn("无法解析任务ID: {}", taskId);
                }
            }
        }
        return 0; // 默认任务ID
    }
    
    /**
     * 从告警中提取域名
     */
    private String extractDomain(Alarm alarm) {
        if (alarm.getExtendedProperties() != null) {
            Object domain = alarm.getExtendedProperties().get("domain");
            if (domain != null) {
                return domain.toString();
            }
        }
        return null;
    }
    
    /**
     * 从告警中提取证书SHA1
     */
    private String extractCertificateSha1(Alarm alarm) {
        if (alarm.getCertificateInfo() != null) {
            return alarm.getCertificateInfo().getSha1();
        }
        return null;
    }
    
    /**
     * 从告警中提取攻击链
     */
    private List<String> extractAttackChain(Alarm alarm) {
        if (alarm.getAttackChainInfo() != null && alarm.getAttackChainInfo().getRelatedAlarms() != null) {
            return alarm.getAttackChainInfo().getRelatedAlarms();
        }
        return null;
    }
}

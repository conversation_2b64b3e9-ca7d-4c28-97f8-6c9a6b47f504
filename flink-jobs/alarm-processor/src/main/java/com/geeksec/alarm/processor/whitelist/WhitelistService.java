package com.geeksec.alarm.processor.whitelist;

import com.geeksec.alarm.processor.model.Alarm;

import java.io.Serializable;
import java.util.List;

/**
 * 白名单服务接口
 * 提供各种类型的白名单检查功能
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
public interface WhitelistService extends Serializable {
    
    /**
     * 检查告警是否在白名单中
     * 
     * @param alarm 告警对象
     * @return 是否在白名单中（true表示在白名单中，应该过滤掉）
     */
    boolean isInWhitelist(Alarm alarm);
    
    /**
     * 检查IP是否在白名单中
     * 
     * @param ip IP地址
     * @param taskId 任务ID
     * @return 是否在白名单中
     */
    boolean isIpInWhitelist(String ip, Integer taskId);
    
    /**
     * 检查域名是否在白名单中
     * 
     * @param domain 域名
     * @param taskId 任务ID
     * @return 是否在白名单中
     */
    boolean isDomainInWhitelist(String domain, Integer taskId);
    
    /**
     * 检查证书是否在白名单中
     * 
     * @param certSha1 证书SHA1哈希
     * @param taskId 任务ID
     * @return 是否在白名单中
     */
    boolean isCertificateInWhitelist(String certSha1, Integer taskId);
    
    /**
     * 检查告警是否在告警白名单中
     * 
     * @param victim 受害者IP
     * @param attacker 攻击者IP
     * @param label 告警标签
     * @return 是否在白名单中
     */
    boolean isAlarmInWhitelist(String victim, String attacker, String label);
    
    /**
     * 检查攻击链是否在白名单中
     * 
     * @param attackChainList 攻击链列表
     * @return 是否在白名单中
     */
    boolean isAttackChainInWhitelist(List<String> attackChainList);
    
    /**
     * 刷新白名单缓存
     */
    void refreshWhitelistCache();
    
    /**
     * 获取白名单统计信息
     * 
     * @return 白名单统计信息
     */
    WhitelistStatistics getWhitelistStatistics();
    
    /**
     * 白名单统计信息
     */
    class WhitelistStatistics implements Serializable {
        private static final long serialVersionUID = 1L;
        
        private long ipWhitelistCount;
        private long domainWhitelistCount;
        private long certificateWhitelistCount;
        private long alarmWhitelistCount;
        private long lastUpdateTime;
        
        // Getters and Setters
        public long getIpWhitelistCount() {
            return ipWhitelistCount;
        }
        
        public void setIpWhitelistCount(long ipWhitelistCount) {
            this.ipWhitelistCount = ipWhitelistCount;
        }
        
        public long getDomainWhitelistCount() {
            return domainWhitelistCount;
        }
        
        public void setDomainWhitelistCount(long domainWhitelistCount) {
            this.domainWhitelistCount = domainWhitelistCount;
        }
        
        public long getCertificateWhitelistCount() {
            return certificateWhitelistCount;
        }
        
        public void setCertificateWhitelistCount(long certificateWhitelistCount) {
            this.certificateWhitelistCount = certificateWhitelistCount;
        }
        
        public long getAlarmWhitelistCount() {
            return alarmWhitelistCount;
        }
        
        public void setAlarmWhitelistCount(long alarmWhitelistCount) {
            this.alarmWhitelistCount = alarmWhitelistCount;
        }
        
        public long getLastUpdateTime() {
            return lastUpdateTime;
        }
        
        public void setLastUpdateTime(long lastUpdateTime) {
            this.lastUpdateTime = lastUpdateTime;
        }
        
        @Override
        public String toString() {
            return String.format("WhitelistStatistics{ip=%d, domain=%d, cert=%d, alarm=%d, lastUpdate=%d}",
                    ipWhitelistCount, domainWhitelistCount, certificateWhitelistCount, 
                    alarmWhitelistCount, lastUpdateTime);
        }
    }
}

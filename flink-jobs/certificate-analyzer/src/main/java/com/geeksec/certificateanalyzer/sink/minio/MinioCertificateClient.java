package com.geeksec.certificateanalyzer.sink.minio;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardOpenOption;

import com.geeksec.common.storage.minio.MinioUtils;

import lombok.extern.slf4j.Slf4j;

/**
 * MinIO证书客户端
 * 提供证书文件在MinIO中的存储和读取功能
 *
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Slf4j
public class MinioCertificateClient {

    /**
     * 证书存储路径前缀
     */
    private static final String CERT_PATH_PREFIX = "certificates";

    private final String bucketName;

    /**
     * 构造函数
     *
     * @param bucketName 存储桶名称
     */
    public MinioCertificateClient(String bucketName) {
        this.bucketName = bucketName;
    }

    /**
     * 上传证书数据到MinIO
     *
     * @param objectName 对象名称
     * @param certData 证书数据
     * @return 上传成功返回true，失败返回false
     */
    public boolean uploadCertificate(String objectName, byte[] certData) {
        if (objectName == null || objectName.trim().isEmpty()) {
            log.warn("对象名称不能为空");
            return false;
        }

        if (certData == null || certData.length == 0) {
            log.warn("证书数据不能为空");
            return false;
        }

        try {
            // 创建临时文件
            Path tempFile = Files.createTempFile("cert_", ".tmp");

            // 将字节数据写入临时文件
            Files.write(tempFile, certData, StandardOpenOption.CREATE, StandardOpenOption.TRUNCATE_EXISTING);

            boolean success = MinioUtils.uploadFile(bucketName, objectName, tempFile.toString());

            // 删除临时文件
            Files.deleteIfExists(tempFile);

            if (success) {
                log.debug("证书上传成功: {}", objectName);
            } else {
                log.warn("证书上传失败: {}", objectName);
            }

            return success;
        } catch (IOException e) {
            log.error("证书上传过程中发生IO异常: {}", objectName, e);
            return false;
        } catch (Exception e) {
            log.error("证书上传失败: {}", objectName, e);
            return false;
        }
    }

    /**
     * 从MinIO下载证书数据
     *
     * @param objectName 对象名称
     * @return 证书数据，下载失败返回null
     */
    public byte[] downloadCertificate(String objectName) {
        if (objectName == null || objectName.trim().isEmpty()) {
            log.warn("对象名称不能为空");
            return null;
        }

        try {
            // 创建临时文件
            Path tempFile = Files.createTempFile("cert_download_", ".tmp");

            boolean success = MinioUtils.downloadFile(bucketName, objectName, tempFile.toString());

            if (!success) {
                log.warn("从MinIO下载证书失败: {}", objectName);
                Files.deleteIfExists(tempFile);
                return null;
            }

            // 读取临时文件内容
            byte[] certData = Files.readAllBytes(tempFile);

            // 删除临时文件
            Files.deleteIfExists(tempFile);

            log.debug("证书下载成功: {}, 大小: {} bytes", objectName, certData.length);
            return certData;

        } catch (IOException e) {
            log.error("证书下载过程中发生IO异常: {}", objectName, e);
            return null;
        } catch (Exception e) {
            log.error("证书下载失败: {}", objectName, e);
            return null;
        }
    }

    /**
     * 检查证书是否存在
     *
     * @param objectName 对象名称
     * @return 存在返回true，不存在返回false
     */
    public boolean existsCertificate(String objectName) {
        if (objectName == null || objectName.trim().isEmpty()) {
            return false;
        }

        try {
            boolean exists = MinioUtils.fileExists(bucketName, objectName);
            log.debug("检查证书是否存在: {}, 结果: {}", objectName, exists);
            return exists;
        } catch (Exception e) {
            log.error("检查证书存在性失败: {}", objectName, e);
            return false;
        }
    }

    /**
     * 删除证书
     *
     * @param objectName 对象名称
     * @return 删除成功返回true，失败返回false
     */
    public boolean deleteCertificate(String objectName) {
        if (objectName == null || objectName.trim().isEmpty()) {
            log.warn("对象名称不能为空");
            return false;
        }

        try {
            boolean success = MinioUtils.deleteFile(bucketName, objectName);

            if (success) {
                log.debug("证书删除成功: {}", objectName);
            } else {
                log.warn("证书删除失败: {}", objectName);
            }

            return success;
        } catch (Exception e) {
            log.error("证书删除失败: {}", objectName, e);
            return false;
        }
    }

    /**
     * 生成证书对象名称
     * 证书文件名使用证书哈希值
     *
     * @param certHash 证书哈希值
     * @return 对象名称
     */
    public static String generateObjectName(String certHash) {
        if (certHash == null || certHash.trim().isEmpty()) {
            throw new IllegalArgumentException("证书哈希值不能为空");
        }

        // 使用哈希值前两位作为目录分层，避免单个目录文件过多
        return String.format("%s/%s/%s",
                CERT_PATH_PREFIX,
                certHash.substring(0, 2),
                certHash);
    }

    /**
     * 批量上传证书
     * 
     * @param certificates 证书数据映射（对象名称 -> 证书数据）
     * @return 成功上传的数量
     */
    public int batchUploadCertificates(java.util.Map<String, byte[]> certificates) {
        if (certificates == null || certificates.isEmpty()) {
            return 0;
        }

        int successCount = 0;
        for (java.util.Map.Entry<String, byte[]> entry : certificates.entrySet()) {
            if (uploadCertificate(entry.getKey(), entry.getValue())) {
                successCount++;
            }
        }

        log.info("批量上传证书完成，成功: {}, 总数: {}", successCount, certificates.size());
        return successCount;
    }

    /**
     * 获取存储桶名称
     * 
     * @return 存储桶名称
     */
    public String getBucketName() {
        return bucketName;
    }
}

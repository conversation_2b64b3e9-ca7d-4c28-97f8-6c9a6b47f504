package com.geeksec.common.utils.alarm;

import com.alibaba.fastjson.JSONObject;
import com.geeksec.common.network.HttpExecutor;
import com.geeksec.common.network.HttpResponse;

import lombok.extern.slf4j.Slf4j;

/**
 * 告警发送器。
 * <p>
 * 负责将构建好的告警信息通过不同渠道发送出去。
 * 此类遵循单一职责原则，专注于告警的发送逻辑。
 *
 * <AUTHOR>
 */
@Slf4j
public final class AlarmNotifier {

    private AlarmNotifier() {
        throw new AssertionError("工具类不应被实例化");
    }

    /**
     * 向指定URL发送告警信息并获取响应。
     *
     * @param wsHost   告警服务URL
     * @param sendJson 要发送的JSON数据
     */
    public static void sendAlarm(String wsHost, JSONObject sendJson) {
        HttpResponse responseData = HttpExecutor.post(wsHost, null, sendJson.toJSONString());
        log.info("写入告警返回结果: {}", responseData);
    }

    /**
     * 向指定URL发送告警信息，不等待响应。
     *
     * @param wsHost   告警服务URL
     * @param sendJson 要发送的JSON数据
     */
    public static void sendAlarmWithoutResponse(String wsHost, JSONObject sendJson) {
        HttpExecutor.post(wsHost, null, sendJson.toJSONString());
    }
}

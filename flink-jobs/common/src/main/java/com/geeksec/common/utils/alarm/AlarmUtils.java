package com.geeksec.common.utils.alarm;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

import com.geeksec.common.utils.crypto.HashUtils;

import lombok.extern.slf4j.Slf4j;

/**
 * 告警工具类，提供通用的告警处理方法
 *
 * <AUTHOR>
 */
@Slf4j
public class AlarmUtils {

    /**
     * 获取默认的告警信息映射
     *
     * @return 包含默认告警字段的Map
     */
    public static Map<String, Object> getDefaultAlarmMap() {
        Map<String, Object> defaultAlarmMap = new HashMap<>();
        defaultAlarmMap.put("alarm_reason", new ArrayList<>());
        defaultAlarmMap.put("alarm_status", 0); // 0:未处理 1:确认 2:误报
        defaultAlarmMap.put("alarm_type", new ArrayList<>()); // 防御/模型/规则
        defaultAlarmMap.put("targets", new ArrayList<>());
        defaultAlarmMap.put("victim", new ArrayList<>());
        defaultAlarmMap.put("attacker", new ArrayList<>());
        defaultAlarmMap.put("attack_family", new ArrayList<>());
        defaultAlarmMap.put("ioc", new ArrayList<>());
        return defaultAlarmMap;
    }

    /**
     * 准备发送数据，添加任务ID和批次ID
     *
     * @param alarmData 告警数据
     * @param taskId    任务ID
     * @param batchId   批次ID
     * @return 准备好的发送数据
     */
    public static Map<String, Object> getSendData(Map<String, Object> alarmData, String taskId, String batchId) {
        Map<String, Object> sendData = new HashMap<>();
        sendData.put("TaskId", taskId);
        sendData.put("BatchId", batchId);
        alarmData.put("task_id", Integer.valueOf(taskId));
        sendData.put("Alarm", alarmData);
        return sendData;
    }

    /**
     * 根据字符串生成告警ID
     *
     * @param input 输入字符串
     * @return 生成的告警ID
     */
    public static String generateAlarmId(String input) {
        return HashUtils.md5(input);
    }

}

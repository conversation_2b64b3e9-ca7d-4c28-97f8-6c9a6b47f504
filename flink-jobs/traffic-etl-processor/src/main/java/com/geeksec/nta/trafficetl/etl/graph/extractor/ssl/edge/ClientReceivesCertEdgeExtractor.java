package com.geeksec.nta.trafficetl.etl.graph.extractor.ssl.edge;

import com.geeksec.common.network.DomainUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.geeksec.common.utils.net.DomainUtils;
import com.geeksec.nta.trafficetl.etl.constant.FieldConstants;
import com.geeksec.nta.trafficetl.etl.graph.extractor.base.BaseEdgeExtractor;
import com.geeksec.nta.trafficetl.sink.tag.NebulaGraphOutputTag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.types.Row;
import org.apache.flink.util.OutputTag;

import java.util.Collections;
import java.util.List;
import org.apache.commons.collections4.CollectionUtils;

/**
 * @Description null.java
 * @Date 17:11$ 2025/6/17$
 **/
@Slf4j
public class ClientReceivesCertEdgeExtractor extends BaseEdgeExtractor {
    private static final ObjectMapper objectMapper = new ObjectMapper();
    @Override
    public OutputTag<Row> getOutputTag() {
        return NebulaGraphOutputTag.Edge.CLIENT_RECEIVES_CERT_TAG;
    }

    /**
     * 客户端在TLS会话中接收/验证服务端的证书 (源IP -> 服务端 CERT)
     *
     * @param value 元数据
     * @return
     */
    @Override
    public List<Row> extractEdge(Row value) {
        try {
            String sIP = value.getField(FieldConstants.FIELD_SRC_IP).toString();
            String dIP = value.getField(FieldConstants.FIELD_DST_IP).toString();
            String sni = value.getField(FieldConstants.FIELD_SSL_HELLO_C_SERVERNAME).toString();
            String dCertHash = value.getField(FieldConstants.FIELD_SSL_CERT_S_HASH).toString();
            List<String> dCertHashes = objectMapper.readValue(dCertHash, new TypeReference<List<String>>() {});

            if (CollectionUtils.isEmpty(dCertHashes)){
                return Collections.emptyList();
            }

            if (DomainUtils.isValidDomain(sni) && !sni.equals(dIP)){
                sni = DomainUtils.formatDomain(sni);
            } else {
                sni = StringUtils.EMPTY;
            }

            return List.of(Row.of(sIP, dCertHashes.get(0).toString(),
                    0, // rank暂定0
                    sni
            ));
        } catch (JsonProcessingException e) {
            log.error("Parse SSL CertHash Error {}", e.getMessage());
        }

        return Collections.emptyList();
    }
}

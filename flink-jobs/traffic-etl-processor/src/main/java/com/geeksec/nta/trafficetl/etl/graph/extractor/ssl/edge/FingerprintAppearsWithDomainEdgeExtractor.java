package com.geeksec.nta.trafficetl.etl.graph.extractor.ssl.edge;

import java.util.Collections;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.apache.flink.types.Row;
import org.apache.flink.util.OutputTag;

import com.geeksec.common.network.DomainUtils;
import com.geeksec.nta.trafficetl.etl.constant.FieldConstants;
import com.geeksec.nta.trafficetl.etl.graph.extractor.base.BaseEdgeExtractor;
import com.geeksec.nta.trafficetl.sink.tag.NebulaGraphOutputTag;

import lombok.extern.slf4j.Slf4j;

/**
 * @Description null.java
 * @Date 17:11$ 2025/6/17$
 **/
@Slf4j
public class FingerprintAppearsWithDomainEdgeExtractor extends BaseEdgeExtractor {
    @Override
    public OutputTag<Row> getOutputTag() {
        return NebulaGraphOutputTag.Edge.FINGERPRINT_APPEARS_WITH_DOMAIN_TAG;
    }

    /**
     * TLS指纹与特定域名在同一TLS会话中出现 (客户端指纹 -> SNI DOMAIN)
     *
     * @param value 元数据
     * @return
     */
    @Override
    public List<Row> extractEdge(Row value) {
        String dIP = value.getField(FieldConstants.FIELD_DST_IP).toString();
        String sni = value.getField(FieldConstants.FIELD_SSL_HELLO_C_SERVERNAME).toString();
        String sSSLFinger = value.getField(FieldConstants.FIELD_SSL_S_FINGER).toString();

        if (StringUtils.isNotEmpty(sSSLFinger) && !"0".equals(sSSLFinger)) {
            return Collections.emptyList();
        }

        if (!DomainUtils.isValidDomain(sni) || sni.equals(dIP)) {
            return Collections.emptyList();
        }

        sni = DomainUtils.formatDomain(sni);
        return List.of(Row.of(sSSLFinger, sni,
                0 // rank暂定0
        ));
    }
}

package com.geeksec.nta.trafficetl.etl.ods.converter.protocol;

import com.geeksec.common.utils.time.TimeUtils;
import com.geeksec.nta.trafficetl.etl.constant.FieldConstants;
import com.geeksec.nta.trafficetl.etl.ods.converter.common.AbstractProtobufMessageConverter;
import com.geeksec.nta.trafficetl.etl.ods.tag.MessageOutputTag;
import com.geeksec.proto.ZMPNMsg;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.types.Row;
import org.apache.flink.util.OutputTag;

/**
 * EIP协议转换器
 * 将EIP协议的protobuf消息转换为Doris ods_eip_protocol_metadata表格式
 *
 * <AUTHOR>
 */
@Slf4j
public class EipConverter extends AbstractProtobufMessageConverter {

    @Override
    protected Row convertMessage(ZMPNMsg.JKNmsg msg) {
        if (!msg.hasEip()) {
            log.warn("JKNmsg does not contain Eip message");
            return null;
        }
        Row row = Row.withNames();
        ZMPNMsg.eip_msg eipMsg = msg.getEip();
        if (eipMsg.hasCommMsg()){
            // 设置通用字段
            enrichComMsg(row, eipMsg.getCommMsg());
        }

        // 设置eipMsg特定字段（需要根据实际protobuf定义实现）
        // TODO: 根据实际的eip protobuf消息结构实现字段映射
        row.setField(FieldConstants.TRANS_ID,eipMsg.getTransId());
        row.setField(FieldConstants.PROTOCOL_ID,eipMsg.getProtocolId());
        row.setField(FieldConstants.SLAVE_ID,eipMsg.getSlaveId());
        row.setField(FieldConstants.FUNC_CODE,eipMsg.getFuncCode());

        return row;
    }

    @Override
    public OutputTag<Row> getOutputTag() {
        return MessageOutputTag.EIP_STREAM;
    }
}

package com.geeksec.nta.trafficetl.etl.ods.converter.protocol;

import com.geeksec.common.utils.time.TimeUtils;
import com.geeksec.nta.trafficetl.etl.constant.FieldConstants;
import com.geeksec.nta.trafficetl.etl.ods.converter.common.AbstractProtobufMessageConverter;
import com.geeksec.nta.trafficetl.etl.ods.tag.MessageOutputTag;
import com.geeksec.proto.ZMPNMsg;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.types.Row;
import org.apache.flink.util.OutputTag;

/**
 * Converter for NTP protocol messages.
 * Maps NTP protocol data from protobuf messages to Doris
 * ods_ntp_protocol_metadata table format.
 * This converter is aligned with the latest ods_ntp_protocol_metadata table
 * schema.
 *
 * <AUTHOR> Team
 */
@Slf4j
public class NtpConverter extends AbstractProtobufMessageConverter {

    @Override
    protected Row convertMessage(ZMPNMsg.JKNmsg msg) {
        if (!msg.hasNtp()) {
            log.warn("JKNmsg does not contain Ntp message");
            return null;
        }
        ZMPNMsg.ntp_msg ntp = msg.getNtp();
        // 创建带有命名字段的Row
        Row row = Row.withNames();
        if (ntp.hasCommMsg()){
            // 添加通用字段
            enrichComMsg(row, ntp.getCommMsg());
        }
        // 添加NTP特定字段
        addNtpFields(row, ntp);

        return row;
    }

    /**
     * 添加NTP特定字段
     */
    private void addNtpFields(Row row, ZMPNMsg.ntp_msg ntp) {
        // NTP版本信息
        row.setField(FieldConstants.FIELD_VERSION, ntp.getVersion());

        // 客户端消息信息
        if (ntp.hasClientMsg()) {
            ZMPNMsg.ntp_client_msg clientMsg = ntp.getClientMsg();
            row.setField(FieldConstants.FIELD_CLIENT_STRATUM, clientMsg.getStratum());
            row.setField(FieldConstants.FIELD_CLIENT_POLL_INTERVAL_SEC, clientMsg.getPollIntervalSec());
            row.setField(FieldConstants.FIELD_CLIENT_CLOCK_PRECISION, clientMsg.getClockPrecision());
            row.setField(FieldConstants.FIELD_CLIENT_ROOT_DELAY, clientMsg.getRootDelay());
            row.setField(FieldConstants.FIELD_CLIENT_ROOT_DISPERSION, clientMsg.getRootDispersion());
            row.setField(FieldConstants.FIELD_CLIENT_REFERENCE_IDENTIFIER, clientMsg.getReferenceIdentifier());
            row.setField(FieldConstants.FIELD_CLIENT_REFER_TS_SEC, clientMsg.getReferTsSec());
            row.setField(FieldConstants.FIELD_CLIENT_REFER_TS_NSEC, clientMsg.getReferTsNsec());
            row.setField(FieldConstants.FIELD_CLIENT_ORIGIN_TS_SEC, clientMsg.getOriginTsSec());
            row.setField(FieldConstants.FIELD_CLIENT_ORIGIN_TS_NSEC, clientMsg.getOriginTsNsec());
            row.setField(FieldConstants.FIELD_CLIENT_RECV_TS_SEC, clientMsg.getRecvTsSec());
            row.setField(FieldConstants.FIELD_CLIENT_RECV_TS_NSEC, clientMsg.getRecvTsNsec());
            row.setField(FieldConstants.FIELD_CLIENT_XMIT_TS_SEC, clientMsg.getXmitTsSec());
            row.setField(FieldConstants.FIELD_CLIENT_XMIT_TS_NSEC, clientMsg.getXmitTsNsec());
        }

        // 服务器消息信息
        if (ntp.hasServerMsg()) {
            ZMPNMsg.ntp_server_msg serverMsg = ntp.getServerMsg();
            row.setField(FieldConstants.FIELD_SERVER_STRATUM, serverMsg.getStratum());
            row.setField(FieldConstants.FIELD_SERVER_POLL_INTERVAL_SEC, serverMsg.getPollIntervalSec());
            row.setField(FieldConstants.FIELD_SERVER_CLOCK_PRECISION, serverMsg.getClockPrecision());
            row.setField(FieldConstants.FIELD_SERVER_ROOT_DELAY, serverMsg.getRootDelay());
            row.setField(FieldConstants.FIELD_SERVER_ROOT_DISPERSION, serverMsg.getRootDispersion());
            row.setField(FieldConstants.FIELD_SERVER_REFERENCE_IDENTIFIER, serverMsg.getReferenceIdentifier());
            row.setField(FieldConstants.FIELD_SERVER_REFER_TS_SEC, serverMsg.getReferTsSec());
            row.setField(FieldConstants.FIELD_SERVER_REFER_TS_NSEC, serverMsg.getReferTsNsec());
            row.setField(FieldConstants.FIELD_SERVER_ORIGIN_TS_SEC, serverMsg.getOriginTsSec());
            row.setField(FieldConstants.FIELD_SERVER_ORIGIN_TS_NSEC, serverMsg.getOriginTsNsec());
            row.setField(FieldConstants.FIELD_SERVER_RECV_TS_SEC, serverMsg.getRecvTsSec());
            row.setField(FieldConstants.FIELD_SERVER_RECV_TS_NSEC, serverMsg.getRecvTsNsec());
            row.setField(FieldConstants.FIELD_SERVER_XMIT_TS_SEC, serverMsg.getXmitTsSec());
            row.setField(FieldConstants.FIELD_SERVER_XMIT_TS_NSEC, serverMsg.getXmitTsNsec());
        }
    }

    @Override
    public OutputTag<Row> getOutputTag() {
        return MessageOutputTag.NTP_STREAM;
    }
}

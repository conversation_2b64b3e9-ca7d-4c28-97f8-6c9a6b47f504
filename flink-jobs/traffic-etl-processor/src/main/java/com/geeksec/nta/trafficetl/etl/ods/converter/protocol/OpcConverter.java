package com.geeksec.nta.trafficetl.etl.ods.converter.protocol;

import com.geeksec.common.utils.time.TimeUtils;
import com.geeksec.nta.trafficetl.etl.constant.FieldConstants;
import com.geeksec.nta.trafficetl.etl.ods.converter.common.AbstractProtobufMessageConverter;
import com.geeksec.nta.trafficetl.etl.ods.tag.MessageOutputTag;
import com.geeksec.proto.ZMPNMsg;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.types.Row;
import org.apache.flink.util.OutputTag;

/**
 * OPC协议转换器
 * 将OPC协议的protobuf消息转换为Doris ods_opc_protocol_metadata表格式
 *
 * <AUTHOR>
 */
@Slf4j
public class OpcConverter extends AbstractProtobufMessageConverter {

    @Override
    protected Row convertMessage(ZMPNMsg.JKNmsg msg) {
        if (!msg.hasOpc()) {
            log.warn("JKNmsg does not contain Opc message");
            return null;
        }

        Row row = Row.withNames();
        ZMPNMsg.opc_msg opcMsg = msg.getOpc();
        if (opcMsg.hasCommMsg()) {
            // 设置通用字段
            enrichComMsg(row, opcMsg.getCommMsg());
        }

        // 设置opcMsg特定字段（需要根据实际protobuf定义实现）
        // TODO: 根据实际的opc protobuf消息结构实现字段映射
        row.setField(FieldConstants.TRANS_ID, opcMsg.getTransId());
        row.setField(FieldConstants.PROTOCOL_ID, opcMsg.getProtocolId());
        row.setField(FieldConstants.SLAVE_ID, opcMsg.getSlaveId());
        row.setField(FieldConstants.FUNC_CODE, opcMsg.getFuncCode());
        return row;
    }

    @Override
    public OutputTag<Row> getOutputTag() {
        return MessageOutputTag.OPC_STREAM;
    }
}

package com.geeksec.nta.trafficetl.etl.ods.converter.protocol;

import com.geeksec.common.utils.time.TimeUtils;
import com.geeksec.nta.trafficetl.etl.constant.FieldConstants;
import com.geeksec.nta.trafficetl.etl.ods.converter.common.AbstractProtobufMessageConverter;
import com.geeksec.nta.trafficetl.etl.ods.tag.MessageOutputTag;
import com.geeksec.proto.ZMPNMsg;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.types.Row;
import org.apache.flink.util.OutputTag;

/**
 * S7协议转换器
 * 将S7协议的protobuf消息转换为Doris ods_s7_protocol_metadata表格式
 *
 * <AUTHOR>
 */
@Slf4j
public class S7Converter extends AbstractProtobufMessageConverter {

    @Override
    protected Row convertMessage(ZMPNMsg.JKNmsg msg) {
        if (!msg.hasS7()) {
            log.warn("JKNmsg does not contain S7 message");
            return null;
        }
        // 注意：这里假设protobuf中有s7_msg字段，实际需要根据proto定义调整
        // ZMPNMsg.s7_msg s7 = msg.getS7();
        // ZMPNMsg.Comm_msg commMsg = s7.getCommMsg();

        // 由于没有具体的S7 protobuf定义，这里创建一个基础的转换器框架
        // 实际使用时需要根据真实的protobuf定义来实现
        Row row = Row.withNames();
        ZMPNMsg.s7_msg s7Msg = getS7Msg(msg);
        if (s7Msg.hasCommMsg()) {
            // 设置通用字段（从通用消息中获取）
            enrichComMsg(row, s7Msg.getCommMsg());
        }

        // 设置S7特定字段（需要根据实际protobuf定义实现）
        // TODO: 根据实际的S7 protobuf消息结构实现字段映
        row.setField(FieldConstants.TPKT_VERSION, s7Msg.getTpktVersion());
        row.setField(FieldConstants.COTP_TYPE, s7Msg.getCotpType());
        row.setField(FieldConstants.S7_TYPE, s7Msg.getS7Type());
        row.setField(FieldConstants.S7_FUNCTION, s7Msg.getS7Function());
        row.setField(FieldConstants.SYSTEM_TYPE, s7Msg.getSystemType());
        row.setField(FieldConstants.SYSTEM_GROUP_FUNCTION, s7Msg.getSystemGroupFunction());
        row.setField(FieldConstants.SYSTEM_SUB_FUNCTION, s7Msg.getSystemSubFunction());
        row.setField(FieldConstants.DST_REF, s7Msg.getDstRef());
        row.setField(FieldConstants.SRC_REF, s7Msg.getSrcRef());
        row.setField(FieldConstants.PDU_SIZE, s7Msg.getPduSize());
        row.setField(FieldConstants.SRC_CONNECT_TYPE, s7Msg.getSrcConnectType());
        row.setField(FieldConstants.SRC_RACK, s7Msg.getSrcRack());
        row.setField(FieldConstants.SRC_SLOT, s7Msg.getSrcSlot());
        row.setField(FieldConstants.DST_CONNECT_TYPE, s7Msg.getDstConnectType());
        row.setField(FieldConstants.DST_RACK, s7Msg.getDstRack());
        row.setField(FieldConstants.DST_SLOT, s7Msg.getDstSlot());
        row.setField(FieldConstants.PACKET_C2S, s7Msg.getPacketC2S());

        return row;
    }

    /**
     * 从消息中提取s7协议消息部分
     * 需要根据实际的protobuf结构实现
     */
    private ZMPNMsg.s7_msg getS7Msg(ZMPNMsg.JKNmsg msg) {
        // TODO: 根据实际的protobuf结构实现
        // 例如：return msg.getS7().getCommMsg();
        return msg.getS7();
    }

    @Override
    public OutputTag<Row> getOutputTag() {
        return MessageOutputTag.S7_STREAM;
    }
}

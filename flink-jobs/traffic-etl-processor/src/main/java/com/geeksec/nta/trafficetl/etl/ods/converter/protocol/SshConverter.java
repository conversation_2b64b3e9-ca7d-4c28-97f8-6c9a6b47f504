package com.geeksec.nta.trafficetl.etl.ods.converter.protocol;

import com.geeksec.common.utils.time.TimeUtils;
import com.geeksec.nta.trafficetl.etl.constant.FieldConstants;
import com.geeksec.nta.trafficetl.etl.ods.converter.common.AbstractProtobufMessageConverter;
import com.geeksec.nta.trafficetl.etl.ods.tag.MessageOutputTag;
import com.geeksec.proto.ZMPNMsg;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.types.Row;
import org.apache.flink.util.OutputTag;

/**
 * Converter for SSH protocol messages.
 * 
 * <AUTHOR> Team
 */
@Slf4j
public class SshConverter extends AbstractProtobufMessageConverter {

    @Override
    protected Row convertMessage(ZMPNMsg.JKNmsg msg) {
        if (!msg.hasSsh()) {
            log.warn("JKNmsg does not contain SSH message");
            return null;
        }

        // 获取SSH相关数据
        Row row = Row.withNames();
        ZMPNMsg.ssh_msg ssh = msg.getSsh();
        if (ssh.hasCommMsg()){
            // 添加通用字段
            enrichComMsg(row, ssh.getCommMsg());
        }

        // 添加SSH特定字段
        addClientFields(row, ssh.getClient());
        addServerFields(row, ssh.getServer());
        addDhFields(row, ssh);
        addEcdhFields(row, ssh);
        addHostKeyFields(row, ssh);
        addKexSignatureFields(row, ssh);
        addFingerprintFields(row);

        return row;
    }

    @Override
    public OutputTag<Row> getOutputTag() {
        return MessageOutputTag.SSH_STREAM;
    }

    /**
     * 添加客户端字段
     *
     * @param row    结果Row
     * @param client 客户端KEX消息对象
     */
    private void addClientFields(Row row, ZMPNMsg.ssh_kex_msg client) {
        row.setField(FieldConstants.FIELD_CLIENT_PROTOCOL, client.getProtocol());
        row.setField(FieldConstants.FIELD_CLIENT_COOKIE, bytesToBase64String(client.getCookie()));
        row.setField(FieldConstants.FIELD_CLIENT_KEX_ALGORITHMS,
                convertProtoListToJavaList(client.getKexAlgorithmsList()));
        row.setField(FieldConstants.FIELD_CLIENT_SERVER_HOST_KEY_ALGORITHMS,
                convertProtoListToJavaList(client.getServerHostKeyAlgorithmsList()));
        row.setField(FieldConstants.FIELD_CLIENT_ENCRYPTION_ALGORITHMS_CLIENT_TO_SERVER,
                convertProtoListToJavaList(client.getEncryptionAlgorithmsClientToServerList()));
        row.setField(FieldConstants.FIELD_CLIENT_ENCRYPTION_ALGORITHMS_SERVER_TO_CLIENT,
                convertProtoListToJavaList(client.getEncryptionAlgorithmsServerToClientList()));
        row.setField(FieldConstants.FIELD_CLIENT_MAC_ALGORITHMS_CLIENT_TO_SERVER,
                convertProtoListToJavaList(client.getMacAlgorithmsClientToServerList()));
        row.setField(FieldConstants.FIELD_CLIENT_MAC_ALGORITHMS_SERVER_TO_CLIENT,
                convertProtoListToJavaList(client.getMacAlgorithmsServerToClientList()));
        row.setField(FieldConstants.FIELD_CLIENT_COMPRESSION_ALGORITHMS_CLIENT_TO_SERVER,
                convertProtoListToJavaList(client.getCompressionAlgorithmsClientToServerList()));
        row.setField(FieldConstants.FIELD_CLIENT_COMPRESSION_ALGORITHMS_SERVER_TO_CLIENT,
                convertProtoListToJavaList(client.getCompressionAlgorithmsServerToClientList()));
    }

    /**
     * 添加服务端字段
     *
     * @param row    结果Row
     * @param server 服务端KEX消息对象
     */
    private void addServerFields(Row row, ZMPNMsg.ssh_kex_msg server) {
        row.setField(FieldConstants.FIELD_SERVER_PROTOCOL, server.getProtocol());
        row.setField(FieldConstants.FIELD_SERVER_COOKIE, bytesToBase64String(server.getCookie()));
        row.setField(FieldConstants.FIELD_SERVER_KEX_ALGORITHMS,
                convertProtoListToJavaList(server.getKexAlgorithmsList()));
        row.setField(FieldConstants.FIELD_SERVER_SERVER_HOST_KEY_ALGORITHMS,
                convertProtoListToJavaList(server.getServerHostKeyAlgorithmsList()));
        row.setField(FieldConstants.FIELD_SERVER_ENCRYPTION_ALGORITHMS_CLIENT_TO_SERVER,
                convertProtoListToJavaList(server.getEncryptionAlgorithmsClientToServerList()));
        row.setField(FieldConstants.FIELD_SERVER_ENCRYPTION_ALGORITHMS_SERVER_TO_CLIENT,
                convertProtoListToJavaList(server.getEncryptionAlgorithmsServerToClientList()));
        row.setField(FieldConstants.FIELD_SERVER_MAC_ALGORITHMS_CLIENT_TO_SERVER,
                convertProtoListToJavaList(server.getMacAlgorithmsClientToServerList()));
        row.setField(FieldConstants.FIELD_SERVER_MAC_ALGORITHMS_SERVER_TO_CLIENT,
                convertProtoListToJavaList(server.getMacAlgorithmsServerToClientList()));
        row.setField(FieldConstants.FIELD_SERVER_COMPRESSION_ALGORITHMS_CLIENT_TO_SERVER,
                convertProtoListToJavaList(server.getCompressionAlgorithmsClientToServerList()));
        row.setField(FieldConstants.FIELD_SERVER_COMPRESSION_ALGORITHMS_SERVER_TO_CLIENT,
                convertProtoListToJavaList(server.getCompressionAlgorithmsServerToClientList()));
    }

    /**
     * 添加DH相关字段
     *
     * @param row 结果Row
     * @param ssh SSH消息对象
     */
    private void addDhFields(Row row, ZMPNMsg.ssh_msg ssh) {
        row.setField(FieldConstants.FIELD_DH_E, bytesToBase64String(ssh.getDhE()));
        row.setField(FieldConstants.FIELD_DH_F, bytesToBase64String(ssh.getDhF()));
        row.setField(FieldConstants.FIELD_DH_GEX_MIN, bytesToBase64String(ssh.getDhGexMin()));
        row.setField(FieldConstants.FIELD_DH_GEX_NBITS, bytesToBase64String(ssh.getDhGexNbits()));
        row.setField(FieldConstants.FIELD_DH_GEX_MAX, bytesToBase64String(ssh.getDhGexMax()));
        row.setField(FieldConstants.FIELD_DH_GEX_P, bytesToBase64String(ssh.getDhGexP()));
        row.setField(FieldConstants.FIELD_DH_GEX_G, bytesToBase64String(ssh.getDhGexG()));
    }

    /**
     * 添加ECDH相关字段
     *
     * @param row 结果Row
     * @param ssh SSH消息对象
     */
    private void addEcdhFields(Row row, ZMPNMsg.ssh_msg ssh) {
        row.setField(FieldConstants.FIELD_ECDH_Q_C, bytesToBase64String(ssh.getEcdhQC()));
        row.setField(FieldConstants.FIELD_ECDH_Q_S, bytesToBase64String(ssh.getEcdhQS()));
    }

    /**
     * 添加主机密钥相关字段
     *
     * @param row 结果Row
     * @param ssh SSH消息对象
     */
    private void addHostKeyFields(Row row, ZMPNMsg.ssh_msg ssh) {
        row.setField(FieldConstants.FIELD_HOST_KEY_TYPE, ssh.getHostKeyType());
        row.setField(FieldConstants.FIELD_HOST_KEY_RSA_E, bytesToBase64String(ssh.getHostKeyRsaE()));
        row.setField(FieldConstants.FIELD_HOST_KEY_RSA_N, bytesToBase64String(ssh.getHostKeyRsaN()));
        row.setField(FieldConstants.FIELD_HOST_KEY_ECDSA_ID, ssh.getHostKeyEcdsaId());
        row.setField(FieldConstants.FIELD_HOST_KEY_ECDSA_Q, bytesToBase64String(ssh.getHostKeyEcdsaQ()));
        row.setField(FieldConstants.FIELD_HOST_KEY_DSA_P, bytesToBase64String(ssh.getHostKeyDsaP()));
        row.setField(FieldConstants.FIELD_HOST_KEY_DSA_Q, bytesToBase64String(ssh.getHostKeyDsaQ()));
        row.setField(FieldConstants.FIELD_HOST_KEY_DSA_G, bytesToBase64String(ssh.getHostKeyDsaG()));
        row.setField(FieldConstants.FIELD_HOST_KEY_DSA_Y, bytesToBase64String(ssh.getHostKeyDsaY()));
        row.setField(FieldConstants.FIELD_HOST_KEY_EDDSA_KEY, bytesToBase64String(ssh.getHostKeyEddsaKey()));
    }

    /**
     * 添加KEX签名相关字段
     *
     * @param row 结果Row
     * @param ssh SSH消息对象
     */
    private void addKexSignatureFields(Row row, ZMPNMsg.ssh_msg ssh) {
        row.setField(FieldConstants.FIELD_KEX_H_SIG_TYPE, ssh.getKexHSigType());
        row.setField(FieldConstants.FIELD_KEX_H_SIG, bytesToBase64String(ssh.getKexHSig()));
    }

    /**
     * 添加指纹相关字段
     *
     * @param row 结果Row
     */
    private void addFingerprintFields(Row row) {
        // 注意：这些字段可能需要通过计算或其他方式获取，这里仅添加占位符
        // 实际实现时可能需要根据具体业务逻辑计算这些值
        // 服务端主机密钥指纹
        row.setField(FieldConstants.FIELD_SRVHOSTKEYFP256, "");
        // 客户端HASSH指纹
        row.setField(FieldConstants.FIELD_HASSH, "");
        // 服务端HASSH指纹
        row.setField(FieldConstants.FIELD_SRV_HASSH, "");
        // key exchange字段md5哈希值
        row.setField(FieldConstants.FIELD_SSH_KEY_FINGERPRINT_MD5_SERVER, "");
    }
}

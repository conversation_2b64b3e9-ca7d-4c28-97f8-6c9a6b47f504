package com.geeksec.nta.trafficetl.etl.ods.converter.protocol;

import com.geeksec.common.utils.time.TimeUtils;
import com.geeksec.nta.trafficetl.etl.constant.FieldConstants;
import com.geeksec.nta.trafficetl.etl.ods.converter.common.AbstractProtobufMessageConverter;
import com.geeksec.nta.trafficetl.etl.ods.tag.MessageOutputTag;
import com.geeksec.proto.ZMPNMsg;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.types.Row;
import org.apache.flink.util.OutputTag;

/**
 * Converter for SSL protocol messages.
 * Maps SSL protocol data from protobuf messages to Doris
 * ods_ssl_protocol_metadata table format.
 * This converter is aligned with the latest ods_ssl_protocol_metadata table
 * schema.
 *
 * <AUTHOR> Team
 */
@Slf4j
public class SslConverter extends AbstractProtobufMessageConverter {

    @Override
    protected Row convertMessage(ZMPNMsg.JKNmsg msg) {
        if (!msg.hasSsl()) {
            log.warn("JKNmsg does not contain SSL message");
            return null;
        }
        Row row = Row.withNames();
        ZMPNMsg.ssl_msg ssl = msg.getSsl();
        if (ssl.hasCommMsg()){
            // 添加通用字段
            enrichComMsg(row, ssl.getCommMsg());
        }

        // 添加各类字段
        addSslVersionFields(row, ssl);
        addClientHelloFields(row, ssl);
        addClientCertificateFields(row, ssl);
        addServerHelloFields(row, ssl);
        addServerCertificateFields(row, ssl);
        addFingerprintFields(row, ssl);

        return row;
    }

    @Override
    public OutputTag<Row> getOutputTag() {
        return MessageOutputTag.SSL_STREAM;
    }

    /**
     * 添加SSL版本信息字段
     *
     * @param row 结果Row
     * @param ssl SSL消息对象
     */
    private void addSslVersionFields(Row row, ZMPNMsg.ssl_msg ssl) {
        row.setField(FieldConstants.FIELD_SSL_VERSION, ssl.getSslVersion());
        row.setField(FieldConstants.FIELD_SSL_C_VERSION, ssl.getSslCVersion());
    }

    /**
     * 添加客户端Hello信息字段
     *
     * @param row 结果Row
     * @param ssl SSL消息对象
     */
    private void addClientHelloFields(Row row, ZMPNMsg.ssl_msg ssl) {
        row.setField(FieldConstants.FIELD_SSL_HELLO_C_VERSION, ssl.getSslHelloCVersion());
        row.setField(FieldConstants.FIELD_SSL_HELLO_C_TIME, ssl.getSslHelloCTime());
        row.setField(FieldConstants.FIELD_SSL_HELLO_C_RANDOM, bytesToBase64String(ssl.getSslHelloCRandom()));
        row.setField(FieldConstants.FIELD_SSL_HELLO_C_SESSIONID, bytesToBase64String(ssl.getSslHelloCSessionid()));
        row.setField(FieldConstants.FIELD_SSL_HELLO_C_SESSIONIDLEN, ssl.getSslHelloCSessionidlen());
        row.setField(FieldConstants.FIELD_SSL_HELLO_C_CIPHERSUIT, bytesToBase64String(ssl.getSslHelloCCiphersuit()));
        row.setField(FieldConstants.FIELD_SSL_HELLO_C_CIPHERSUITNUM, ssl.getSslHelloCCiphersuitnum());
        row.setField(FieldConstants.FIELD_SSL_HELLO_C_COMPRESSIONMETHOD,
                bytesToBase64String(ssl.getSslHelloCCompressionmethod()));
        row.setField(FieldConstants.FIELD_SSL_HELLO_C_COMPRESSIONMETHODLEN, ssl.getSslHelloCCompressionmethodlen());
        row.setField(FieldConstants.FIELD_SSL_HELLO_C_EXTENTIONNUM, ssl.getSslHelloCExtentionnum());
        row.setField(FieldConstants.FIELD_SSL_HELLO_C_EXTENTION, bytesToBase64String(ssl.getSslHelloCExtention()));
        row.setField(FieldConstants.FIELD_SSL_HELLO_C_ALPN, bytesToBase64String(ssl.getSslHelloCAlpn()));
        row.setField(FieldConstants.FIELD_SSL_HELLO_C_SERVERNAME, ssl.getSslHelloCServername());
        row.setField(FieldConstants.FIELD_SSL_HELLO_C_SERVERNAMETYPE, ssl.getSslHelloCServernametype());
        row.setField(FieldConstants.FIELD_SSL_HELLO_C_SESSIONTICKET,
                bytesToBase64String(ssl.getSslHelloCSessionticket()));
    }

    /**
     * 添加客户端证书信息字段
     *
     * @param row 结果Row
     * @param ssl SSL消息对象
     */
    private void addClientCertificateFields(Row row, ZMPNMsg.ssl_msg ssl) {
        row.setField(FieldConstants.FIELD_SSL_CERT_C_NUM, ssl.getSslCertCNum());
        row.setField(FieldConstants.FIELD_SSL_CERT_C_HASH, bytesToBase64String(ssl.getSslCertCHash()));
        row.setField(FieldConstants.FIELD_SSL_C_KEYEXCHANGELEN, ssl.getSslCKeyexchangelen());
        row.setField(FieldConstants.FIELD_SSL_C_KEYEXCHANGE, bytesToBase64String(ssl.getSslCKeyexchange()));
    }

    /**
     * 添加服务端Hello信息字段
     *
     * @param row 结果Row
     * @param ssl SSL消息对象
     */
    private void addServerHelloFields(Row row, ZMPNMsg.ssl_msg ssl) {
        row.setField(FieldConstants.FIELD_SSL_HELLO_S_VERSION, ssl.getSslHelloSVersion());
        row.setField(FieldConstants.FIELD_SSL_HELLO_S_TIME, ssl.getSslHelloSTime());
        row.setField(FieldConstants.FIELD_SSL_HELLO_S_RANDOM, bytesToBase64String(ssl.getSslHelloSRandom()));
        row.setField(FieldConstants.FIELD_SSL_HELLO_S_SESSIONID, bytesToBase64String(ssl.getSslHelloSSessionid()));
        row.setField(FieldConstants.FIELD_SSL_HELLO_S_SESSIONIDLEN, ssl.getSslHelloSSessionidlen());
        row.setField(FieldConstants.FIELD_SSL_HELLO_S_CIPERSUITE, bytesToBase64String(ssl.getSslHelloSCipersuite()));
        row.setField(FieldConstants.FIELD_SSL_HELLO_S_COMPRESSIONMETHOD,
                bytesToBase64String(ssl.getSslHelloSCompressionmethod()));
        row.setField(FieldConstants.FIELD_SSL_HELLO_S_EXTENTIONNUM, ssl.getSslHelloSExtentionnum());
        row.setField(FieldConstants.FIELD_SSL_HELLO_S_EXTENTION, bytesToBase64String(ssl.getSslHelloSExtention()));
        row.setField(FieldConstants.FIELD_SSL_HELLO_S_ALPN, bytesToBase64String(ssl.getSslHelloSAlpn()));
        row.setField(FieldConstants.FIELD_SSL_HELLO_S_SESSIONTICKET,
                bytesToBase64String(ssl.getSslHelloSSessionticket()));
    }

    /**
     * 添加服务端证书信息字段
     *
     * @param row 结果Row
     * @param ssl SSL消息对象
     */
    private void addServerCertificateFields(Row row, ZMPNMsg.ssl_msg ssl) {
        row.setField(FieldConstants.FIELD_SSL_CERT_S_NUM, ssl.getSslCertSNum());
        row.setField(FieldConstants.FIELD_SSL_CERT_S_HASH, bytesToBase64String(ssl.getSslCertSHash()));
        row.setField(FieldConstants.FIELD_SSL_S_NEWSESSIONTICKET_LIFETIME, ssl.getSslSNewsessionticketLifetime());
        row.setField(FieldConstants.FIELD_SSL_S_NEWSESSIONTICKET_TICKET,
                bytesToBase64String(ssl.getSslSNewsessionticketTicket()));
        row.setField(FieldConstants.FIELD_SSL_S_NEWSESSIONTICKET_TICKETLEN, ssl.getSslSNewsessionticketTicketlen());
        row.setField(FieldConstants.FIELD_SSL_S_KEYEXCHANGELEN, ssl.getSslSKeyexchangelen());
        row.setField(FieldConstants.FIELD_SSL_S_KEYEXCHANGE, bytesToBase64String(ssl.getSslSKeyexchange()));
    }

    /**
     * 添加指纹相关字段
     *
     * @param row 结果Row
     * @param ssl SSL消息对象
     */
    private void addFingerprintFields(Row row, ZMPNMsg.ssl_msg ssl) {
        row.setField(FieldConstants.FIELD_SSL_C_FINGER, ssl.getSslCFinger());
        row.setField(FieldConstants.FIELD_SSL_S_FINGER, ssl.getSslSFinger());

        // JA3指纹字段 - 这些字段可能需要通过计算获取
        row.setField(FieldConstants.FIELD_CLI_JA3, "");
        row.setField(FieldConstants.FIELD_SRV_JA3, "");

        // 其他指纹字段
        row.setField(FieldConstants.FIELD_JOY, "");
        row.setField(FieldConstants.FIELD_JOYS, "");
        row.setField(FieldConstants.FIELD_JOY_FP, "");
    }
}

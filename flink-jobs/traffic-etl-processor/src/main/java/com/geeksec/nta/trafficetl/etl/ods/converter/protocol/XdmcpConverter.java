package com.geeksec.nta.trafficetl.etl.ods.converter.protocol;

import com.geeksec.common.utils.time.TimeUtils;
import com.geeksec.nta.trafficetl.etl.constant.FieldConstants;
import com.geeksec.nta.trafficetl.etl.ods.converter.common.AbstractProtobufMessageConverter;
import com.geeksec.nta.trafficetl.etl.ods.tag.MessageOutputTag;
import com.geeksec.proto.ZMPNMsg;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.types.Row;
import org.apache.flink.util.OutputTag;

import java.util.ArrayList;
import java.util.List;

/**
 * Converter for XDMCP protocol messages.
 * 
 * <AUTHOR> Team
 */
@Slf4j
public class XdmcpConverter extends AbstractProtobufMessageConverter {

    @Override
    protected Row convertMessage(ZMPNMsg.JKNmsg msg) {
        if (!msg.hasXdmcp()) {
            log.warn("JKNmsg does not contain XDMCP message");
            return null;
        }
        // 获取XDMCP相关数据
        ZMPNMsg.xdmcp_msg xdmcp = msg.getXdmcp();
        Row row = Row.withNames();
        if (xdmcp.hasCommMsg()){
            // 添加通用字段
            enrichComMsg(row, xdmcp.getCommMsg());
        }

        // 添加各类字段
        addXdmcpFields(row, xdmcp);
        addConnectionFields(row, xdmcp.getConnectionsList());
        addAuthFields(row, xdmcp.getClientAuth(), xdmcp.getServerAuth());

        return row;
    }

    @Override
    public OutputTag<Row> getOutputTag() {
        return MessageOutputTag.XDMCP_STREAM;
    }

    /**
     * 添加XDMCP特定字段
     *
     * @param row   结果Row
     * @param xdmcp XDMCP消息对象
     */
    private void addXdmcpFields(Row row, ZMPNMsg.xdmcp_msg xdmcp) {
        // XDMCP特定字段
        row.setField(FieldConstants.FIELD_VERSION, xdmcp.getVersion());
        row.setField(FieldConstants.FIELD_DISPLAY_NUMBER, xdmcp.getDisplayNumber());
        row.setField(FieldConstants.FIELD_XDMCP_SESSION_ID, xdmcp.getSessionId());
        row.setField(FieldConstants.FIELD_HOSTNAME, xdmcp.getHostname());
        row.setField(FieldConstants.FIELD_MANUFACTURE_DISP_ID, xdmcp.getManufactureDispId());
        row.setField(FieldConstants.FIELD_DISPLAY_CLASS, xdmcp.getDisplayClass());
        row.setField(FieldConstants.FIELD_STATUS, xdmcp.getStatus());
    }

    /**
     * 添加连接信息字段
     *
     * @param row             结果Row
     * @param connectionsList 连接信息列表
     */
    private void addConnectionFields(Row row, List<ZMPNMsg.xdmcp_connection_msg> connectionsList) {
        // 连接信息
        List<String> connectionTypes = new ArrayList<>();
        List<String> connectionAddresses = new ArrayList<>();

        for (ZMPNMsg.xdmcp_connection_msg connection : connectionsList) {
            if (connection.hasType()) {
                connectionTypes.add(connection.getType());
            }
            if (connection.hasAddr()) {
                connectionAddresses.add(connection.getAddr());
            }
        }

        row.setField(FieldConstants.FIELD_CONNECTION_TYPES, connectionTypes);
        row.setField(FieldConstants.FIELD_CONNECTION_ADDRESSES, connectionAddresses);
    }

    /**
     * 添加认证信息字段
     *
     * @param row        结果Row
     * @param clientAuth 客户端认证信息
     * @param serverAuth 服务端认证信息
     */
    private void addAuthFields(Row row, ZMPNMsg.xdmcp_auth_msg clientAuth, ZMPNMsg.xdmcp_auth_msg serverAuth) {
        // 客户端认证信息
        if (clientAuth != null) {
            row.setField(FieldConstants.FIELD_CLIENT_AUTHENTICATION_NAMES,
                    convertProtoListToJavaList(clientAuth.getAuthenticationNamesList()));
            row.setField(FieldConstants.FIELD_CLIENT_AUTHENTICATION_DATA,
                    bytesToBase64String(clientAuth.getAuthenticationData()));
            row.setField(FieldConstants.FIELD_CLIENT_AUTHORIZATION_NAMES,
                    convertProtoListToJavaList(clientAuth.getAuthorizationNamesList()));
            row.setField(FieldConstants.FIELD_CLIENT_AUTHORIZATION_DATA,
                    bytesToBase64String(clientAuth.getAuthorizationData()));
        }

        // 服务端认证信息
        if (serverAuth != null) {
            row.setField(FieldConstants.FIELD_SERVER_AUTHENTICATION_NAMES,
                    convertProtoListToJavaList(serverAuth.getAuthenticationNamesList()));
            row.setField(FieldConstants.FIELD_SERVER_AUTHENTICATION_DATA,
                    bytesToBase64String(serverAuth.getAuthenticationData()));
            row.setField(FieldConstants.FIELD_SERVER_AUTHORIZATION_NAMES,
                    convertProtoListToJavaList(serverAuth.getAuthorizationNamesList()));
            row.setField(FieldConstants.FIELD_SERVER_AUTHORIZATION_DATA,
                    bytesToBase64String(serverAuth.getAuthorizationData()));
        }
    }

}

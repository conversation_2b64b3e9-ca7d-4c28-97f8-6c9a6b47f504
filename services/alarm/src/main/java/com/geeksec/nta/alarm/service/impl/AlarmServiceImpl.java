package com.geeksec.nta.alarm.service.impl;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import com.geeksec.common.dto.ApiResponse;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.dao.DataAccessException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.geeksec.common.enums.ErrorCode;
import com.geeksec.common.exceptions.BusinessException;
import com.geeksec.common.entity.PageResultVo;
import com.geeksec.nta.alarm.dto.CommonRangeDto;
import com.geeksec.nta.alarm.dto.condition.AlarmCommonCondition;
import com.geeksec.nta.alarm.dto.condition.AlarmListCondition;
import com.geeksec.nta.alarm.dto.condition.AlarmStatusUpCondition;
import com.geeksec.nta.alarm.dto.condition.DownloadPcapCondition;
import com.geeksec.nta.alarm.entity.DownloadTask;
import com.geeksec.nta.alarm.mapper.AlarmMapper;
import com.geeksec.nta.alarm.mapper.KnowledgeAlarmMapper;
import com.geeksec.nta.alarm.service.AlarmService;
import com.geeksec.nta.alarm.utils.DorisQueryHelper;
import com.geeksec.nta.alarm.vo.AlarmTargetAggVo;
import com.geeksec.nta.alarm.vo.AlarmTypeAggVo;
import com.geeksec.nta.alarm.vo.KnowledgeAlarmVo;
import com.geeksec.nta.alarm.vo.KnowledgeTypeVo;
import com.google.common.collect.Lists;

import cn.hutool.http.HttpRequest;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
@RequiredArgsConstructor
public class AlarmServiceImpl implements AlarmService {

    private static Map<Integer, KnowledgeTypeVo> alarnKnowledgeMap = new HashMap<>();

    final AlarmMapper alarmMapper;

    @Value("${send-url.alarm_report_export}")
    private String alarmReportExprotUrl;


    final KnowledgeAlarmMapper knowledgeAlarmMapper;


    @PostConstruct
    @Override
    public void initKnowledgeType() {
        List<KnowledgeTypeVo> knowledgeTypes = knowledgeAlarmMapper.getKnowledgeType();
        for (KnowledgeTypeVo knowledgeType : knowledgeTypes) {
            alarnKnowledgeMap.put(knowledgeType.getKnowledgeAlarmId(), knowledgeType);
        }
    }


    /**
     * 高效获取告警指标信息。此方法通过一次数据库查询获取所有聚合数据。
     *
     * @param condition 通用告警查询条件。
     * @return 包含所有聚合统计信息的 AlarmTargetAggVo。
     */
    @Override
    public AlarmTargetAggVo getAlarmTargetAgg(AlarmCommonCondition condition) {
        log.info("告警：指标信息 (Doris实现)，condition={}", condition);

        // 步骤 1: 参数校验 (保持不变)
        BusinessException errorVo = checkParam(condition);
        if (errorVo != null) {
            throw  errorVo;
        }

        // 步骤 2: 使用新的辅助类，将查询条件转换为Map
        // 这完全替代了 getCommonQueryBuilder() 的调用
        Map<String, Object> queryConditions = DorisQueryHelper.buildAlarmQueryConditions(condition);

        long t1 = System.currentTimeMillis();

        // 步骤 3: 执行单次数据库聚合查询
        // 这条语句替代了原来所有的 for 循环和 esSearchForCount 调用
        Map<String, Long> aggResults = alarmMapper.getAlarmAggregations(queryConditions);

        long t2 = System.currentTimeMillis();
        log.info("告警指标-Doris单次聚合查询耗时t={}", (t2 - t1));

        // 步骤 4: 组装返回结果VO
        AlarmTargetAggVo resultVo = new AlarmTargetAggVo();

        // 从Map中安全地获取计数值 (aggResults 不会为 null, 但键可能缺失，所以用 orElse)
        long lowLevel = aggResults.getOrDefault("low_level_count", 0L);
        long middleLevel = aggResults.getOrDefault("middle_level_count", 0L);
        long highLevel = aggResults.getOrDefault("high_level_count", 0L);

        resultVo.setLowLevel(lowLevel);
        resultVo.setMiddleLevel(middleLevel);
        resultVo.setHighLevel(highLevel);
        resultVo.setAlarmCnt(lowLevel + middleLevel + highLevel); // 总数是各等级之和

        resultVo.setAlarmStatus0(aggResults.getOrDefault("status_0_count", 0L));
        resultVo.setAlarmStatus1(aggResults.getOrDefault("status_1_count", 0L));
        resultVo.setAlarmStatus2(aggResults.getOrDefault("status_2_count", 0L));

        return resultVo;
    }


    @Override
    public List<AlarmTypeAggVo.AttackChain> getModelAlarmAttackChainAggr(AlarmCommonCondition condition) {
        log.info("告警：模型告警攻击链聚合，condition={}", condition);
        List<Integer> taskIds = condition.getTaskIds();
        if (taskIds == null || taskIds.isEmpty()) {
            return new ArrayList<>();
        }

        // 执行查询
        List<Map<String, Object>> queryResults = alarmMapper.getModelAlarmAttackChainAggr(condition);

        // 处理查询结果
        Map<String, AlarmTypeAggVo.AttackChain> chainMap = new LinkedHashMap<>();

        for (Map<String, Object> row : queryResults) {
            //TODO 处理结果
            String chainName = (String) row.get("attack_chain_name");
            Long chainCount = (Long) row.get("chain_count");
            Integer knowledgeId = (Integer) row.get("alarm_knowledge_id");
            Long knowledgeCount = (Long) row.get("knowledge_count");

            // 获取或创建攻击链对象
            AlarmTypeAggVo.AttackChain chain = chainMap.computeIfAbsent(chainName, k -> {
                AlarmTypeAggVo.AttackChain newChain = new AlarmTypeAggVo.AttackChain();
                newChain.setAttackChainName(chainName);
                newChain.setAttackChainCnt(chainCount);
                newChain.setAlarmKnowledgeList(new ArrayList<>());
                return newChain;
            });

            // 创建告警知识对象并添加到攻击链中
            AlarmTypeAggVo.AlarmKnowledge knowledge = new AlarmTypeAggVo.AlarmKnowledge();
            knowledge.setAlarmKnowledgeId(knowledgeId);
            knowledge.setAlarmKnowledgeCnt(knowledgeCount);
            chain.getAlarmKnowledgeList().add(knowledge);
        }

        // 获取知识库名称
        List<KnowledgeAlarmVo> knowledgeAlarmList = getKnowledgeAlarmList();
        Map<Integer, String> knowledgeNameMap = knowledgeAlarmList.stream()
                .collect(Collectors.toMap(KnowledgeAlarmVo::getId, KnowledgeAlarmVo::getAlarmName));

        // 填充知识库名称
        List<AlarmTypeAggVo.AttackChain> chainList = new ArrayList<>(chainMap.values());
        for (AlarmTypeAggVo.AttackChain chain : chainList) {
            for (AlarmTypeAggVo.AlarmKnowledge knowledge : chain.getAlarmKnowledgeList()) {
                Integer id = knowledge.getAlarmKnowledgeId();
                String name = knowledgeNameMap.get(id);
                knowledge.setAlarmKnowledgeName(name != null ? name : "错误的知识库id");
            }
        }

        return chainList;
    }

    @Override
    public List<KnowledgeAlarmVo> getKnowledgeAlarmList() {
        //Integer userId = tokenService.getUserInfoByToken();//TODO 调用权限服务获取用户id
        //return knowledgeAlarmDao.getKnowledgeAlarmList(userId);//TODO 获取知识库服务告警列表
        return Lists.newArrayList();
    }

    @Override
    public PageResultVo<Map<String, Object>> getAlarmList(AlarmListCondition condition) {
        log.info("告警：列表（Doris），condition={}", condition);

        // 1. Parameter validation (remains the same)
        String alarmType = condition.getAlarmType();
        if (StringUtils.isEmpty(alarmType) || !("模型".equals(alarmType) || "防御".equals(alarmType) || "规则".equals(alarmType) || "威胁情报".equals(alarmType))) {
            throw new BusinessException(ErrorCode.PARAM_VALIDATION_FAILED);
        }
        BusinessException errorVo = checkParam(condition);
        if (errorVo != null) {
            throw errorVo;
        }

        // 2. Build the parameter map for MyBatis, incorporating all conditions
        Map<String, Object> params = new HashMap<>();

        // From AlarmListCondition itself
        params.put("alarmType", alarmType);
        params.put("ids", condition.getIds()); // For specific ID-based queries

        // From the common condition (getCommonQueryBuilder logic)
        params.put("left", condition.getLeft());
        params.put("right", condition.getRight());
        params.put("alarmIds", condition.getAlarmIds());
        params.put("taskIds", condition.getTaskIds());
        params.put("targetName", condition.getTargetName());
        params.put("victim", condition.getVictim());
        params.put("attackerIp", condition.getAttackerIp());
        params.put("alarmStatusList", condition.getAlarmStatusList());

        // Special handling for attack level ranges
        List<String> attackLevels = condition.getAttackLevels();
        if (attackLevels != null && !attackLevels.isEmpty()) {
            List<Map<String, String>> attackLevelRanges = new ArrayList<>();
            for (String attackLevel : attackLevels) {
                if (StringUtils.isNotEmpty(attackLevel) && attackLevel.contains("-")) {
                    String[] split = attackLevel.split("-");
                    if (split.length == 2) {
                        Map<String, String> range = new HashMap<>();
                        range.put("min", split[0]);
                        range.put("max", split[1]);
                        attackLevelRanges.add(range);
                    }
                }
            }
            params.put("attackLevelRanges", attackLevelRanges);
        }


        // 3. Handle pagination and sorting (remains the same)
        Integer currentPage = condition.getCurrentPage();
        Integer pageSize = condition.getPageSize();
        if (pageSize < 1) {
            params.put("pageSize", 10000);
            params.put("offset", 0);
        } else {
            params.put("pageSize", pageSize);
            params.put("offset", (currentPage - 1) * pageSize);
        }

        String orderField = condition.getOrderField();
        if (isValidSortField(orderField)) {
            params.put("orderField", orderField);
        } else {
            params.put("orderField", "a.create_time");
        }
        params.put("asc", condition.getAsc());

        // 4. Execute the queries (remains the same)
        long totalHits = alarmMapper.countAlarmList(params);
        List<Map<String, Object>> list = (totalHits > 0) ? alarmMapper.queryAlarmList(params) : new ArrayList<>();

        // 5. Build and return the result (remains the same)
        PageResultVo<Map<String, Object>> pageResultVo = new PageResultVo<>();
        pageResultVo.setRecords(list);
        pageResultVo.setTotal(totalHits);

        return pageResultVo;
    }

    // Whitelist validation for sort fields (remains the same)
    private boolean isValidSortField(String field) {
        if (StringUtils.isEmpty(field)) {
            return false;
        }
        return List.of("a.create_time", "asrc.attack_level", "asrc.time").contains(field);
    }

    @Override
    public Map<String, Object> getAlarmDetail2(String esIndex, String alarmId) {
        // 1. 直接调用Mapper方法，通过一条SQL语句获取所有需要的数据
        Map<String, Object> resultMap = alarmMapper.getAlarmDetail(alarmId, esIndex);

        // 2. 处理查询结果
        // 如果查询结果为null，说明数据库中没有找到对应的记录，按原逻辑返回一个空Map
        if (resultMap == null) {
            return new HashMap<>();
        }

        // 3. 对SQL查询结果进行微调，以完全匹配原方法的返回格式
        // a. 将SQL中通过COALESCE计算出的最终告警名称，赋值给 'alarm_name' 键
        resultMap.put("alarm_name", resultMap.get("final_alarm_name"));

        // b. 将原方法中固定添加的字段也加入
        resultMap.put("_id", alarmId);
        resultMap.put("es_index", esIndex);

        // c. 确保 'alarm_related_label' 字段存在，如果为null则替换为空列表，以匹配原逻辑
        // computeIfAbsent 是一个优雅的处理方式：如果key不存在或值为null，则计算并放入默认值
        resultMap.computeIfAbsent("alarm_related_label", k -> new ArrayList<>());

        // 4. 返回经过最终处理的Map
        return resultMap;
    }

    /**
     * 更新告警的状态，并根据条件（状态改为2时）将攻击链信息录入知识库。
     *
     * @param condition 包含告警的ID、任务ID和新状态。
     * @return 成功信息。
     */
    @Transactional
    @Override
    public String updateDoc(AlarmStatusUpCondition condition) {
        log.info("告警：修改文档状态（Doris），condition={}", condition);

        // 1. 参数校验（逻辑与原代码保持一致）
        String id = condition.getId();
        Integer taskId = condition.getTaskId();
        Integer alarmStatus = condition.getAlarmStatus();
        if (StringUtils.isEmpty(id) || taskId == null || alarmStatus == null || alarmStatus < 0 || alarmStatus > 2) {
            throw new BusinessException(ErrorCode.PARAM_VALIDATION_FAILED);
        }

        // 2. 更新前，先查找记录并获取后续逻辑所需的数据。
        // 这替代了原代码中先执行ES搜索的操作。
        Map<String, Object> alarmSourceData = alarmMapper.findSourceForUpdate(id, taskId);

        // 如果根据id和taskId找不到记录，则抛出异常。
        if (alarmSourceData == null) {
            log.error("告警：修改文档状态，记录未找到。id={}, taskId={}", id, taskId);
            // 建议使用更具体的错误类型，如 DATA_NOT_FOUND_ERROR
            throw new BusinessException(ErrorCode.DATABASE_OPERATION_FAILED);
        }

        // 3. 执行更新操作
        int affectedRows = alarmMapper.updateAlarmStatus(id, taskId, alarmStatus);

        // 在事务中，如果 findSourceForUpdate 成功，此处的更新几乎不可能失败。
        // 但作为健壮性代码，检查受影响的行数是个好习惯。
        if (affectedRows < 1) {
            log.error("告警：修改文档状态失败，更新影响行数为0。id={}, taskId={}", id, taskId);
            // 建议使用更具体的错误类型，如 DB_UPDATE_ERROR
            throw new BusinessException(ErrorCode.DATABASE_OPERATION_FAILED);
        }

        // 4. 如果告警状态被修改为“已处理完成”（假设状态值为2），则执行攻击链的录入逻辑。
        // 这个逻辑现在是整个事务的一部分，保证了数据的一致性。
        if (condition.getAlarmStatus() == 2) {
            // 这里的 'attack_chain_list' 来自我们第2步中预先查询出的数据。
            Object attackChainObj = alarmSourceData.get("attack_chain_list");

            // Doris中的TEXT字段在Java中对应为String类型。我们假设它存储的是JSON数组格式的字符串，例如 '["chain1", "chain2"]'。
            if (attackChainObj instanceof String && StringUtils.isNotEmpty((String) attackChainObj)) {
                try {
                    // 使用Jackson等JSON库将字符串反序列化为List<String>。
                    ObjectMapper objectMapper = new ObjectMapper();
                    List<String> attackChainList = objectMapper.readValue((String) attackChainObj, new TypeReference<List<String>>(){});

                    // 此处的流式处理和DAO调用逻辑与原代码保持一致。
                    attackChainList.stream()
                            .map(chain -> chain.split("_"))
                            .filter(array -> array.length >= 3)
                            .forEach(array -> {
                                String victimIp = array[0];
                                String attackerIp = array[1];
                                String labelStr = array[2];
                                long count = knowledgeAlarmMapper.countAttackChain(attackerIp, victimIp, labelStr);
                                if (count == 0) {
                                    knowledgeAlarmMapper.insertAttackChain(attackerIp, victimIp, labelStr);
                                }
                            });
                } catch (JsonProcessingException e) {
                    log.error("从Doris解析attack_chain_list JSON失败，告警ID: {}", id, e);
                    // 根据业务需求，如果JSON解析失败，可以考虑抛出运行时异常，以便触发整个事务的回滚。
                }
            }
        }

        // 无需 Thread.sleep()。数据库事务保证了操作的原子性和一致性，一旦执行成功，结果立即可见。
        return "修改告警状态成功";
    }

    /**
     * 根据指定的任务ID和告警ID映射，批量删除告警及其所有相关数据。
     * 整个操作在一个事务中执行，保证数据一致性。
     *
     * @param map Key为taskId, Value为该taskId下的alarmId列表。
     * @return 成功删除的告警总数。
     */
    @Transactional
    @Override
    public Long deleteDoc(Map<Integer, List<String>> map) {
        // 1. 参数校验
        if (map == null || map.isEmpty()) {
            throw new BusinessException(ErrorCode.PARAM_VALIDATION_FAILED);
        }

        // 2. 第一步：找出所有需要删除的告警ID
        List<String> idsToDelete = alarmMapper.findAlarmIdsForDeletion(map);

        // 3. 如果没有找到任何匹配的告警，则按原逻辑抛出异常或返回0
        if (idsToDelete == null || idsToDelete.isEmpty()) {
            // 原代码在删除数量为0时抛出异常，我们遵循此逻辑。
            throw new BusinessException(ErrorCode.DATABASE_OPERATION_FAILED);
        }

        // 4. 第二步：根据ID列表，从所有相关表中批量删除数据
        // 先删除所有子表数据
        alarmMapper.deleteFromAlarmSourcesByIds(idsToDelete);
        alarmMapper.deleteFromAlarmAttackersByIds(idsToDelete);
        alarmMapper.deleteFromAlarmReasonsByIds(idsToDelete);
        alarmMapper.deleteFromAlarmTargetsByIds(idsToDelete);
        alarmMapper.deleteFromAlarmVictimsByIds(idsToDelete);

        // 最后删除主表数据
        alarmMapper.deleteFromAlarmsByIds(idsToDelete);

        // 5. 移除 Thread.sleep()，事务保证了操作的即时性和一致性。

        // 6. 返回成功删除的告警总数，即我们第一步找到的ID数量。
        return (long) idsToDelete.size();
    }

    /**
     * 删除所有的告警相关数据。
     * 此操作会清空（TRUNCATE）所有与告警相关的表，不可恢复。
     *
     * @return 成功信息。
     */
    @Transactional // 开启事务，确保所有表的清空操作是一个原子单元。
    public String deleteAllAlarm() {
        log.info("开始删除所有的告警相关数据（Doris）");

        try {
            // 按照 "子表" -> "主表" 的顺序清空是一个好习惯，尽管在没有外键约束时顺序不强制。
            // 这有助于在有外键约束的数据库中避免错误。
            alarmMapper.truncateAlarmSources();
            alarmMapper.truncateAlarmAttackers();
            alarmMapper.truncateAlarmVictims();
            alarmMapper.truncateAlarmTargets();
            alarmMapper.truncateAlarmReasons();

            // 最后清空主表
            alarmMapper.truncateAlarms();

            log.info("所有告警相关表已成功清空。");
            return "删除所有告警成功";

        } catch (DataAccessException e) {
            // 如果任何一个 TRUNCATE 操作失败（例如，数据库用户权限不足），
            // Spring会将底层的JDBC异常包装成DataAccessException。
            log.error("删除所有告警数据时发生数据库错误", e);
            // 抛出业务异常，以便全局异常处理器捕获并返回给前端。
            throw new BusinessException(ErrorCode.DATABASE_OPERATION_FAILED);
        }
    }

    /**
     * 导出告警报告。
     * 此方法首先从Doris根据条件查询出所有匹配的告警ID，然后将ID列表发送给外部报告生成服务。
     *
     * @param condition 包含所有筛选条件的告警列表对象。
     * @return 外部报告服务返回的文件路径。
     */
    @Override
    public String exportAlarmReport(AlarmListCondition condition) {
        log.info("告警报告导出，筛选条件：{}", condition);

        // 1. 参数校验 (与原代码逻辑保持一致)
        String alarmType = condition.getAlarmType();
        if (StringUtils.isEmpty(alarmType) || !("模型".equals(alarmType) || "防御".equals(alarmType) || "规则".equals(alarmType) || "威胁情报".equals(alarmType))) {
            throw new BusinessException(ErrorCode.PARAM_VALIDATION_FAILED);
        }
        BusinessException errorVo = checkParam(condition);
        if (errorVo != null) {
            throw errorVo;
        }
        // **重要**: 在这里添加对排序字段的白名单校验，防止SQL注入
        validateOrderField(condition.getOrderField());

        // 2. 从Doris查询所有符合条件的告警ID
        List<String> alarmIdsToExport = alarmMapper.findAlarmIdsForExport(condition);

        // 3. 如果没有数据，则直接提示用户，无需调用外部服务
        if (alarmIdsToExport == null || alarmIdsToExport.isEmpty()) {
            log.warn("没有找到符合条件的告警数据用于导出。");
            // 可以抛出一个特定的异常或返回一个提示信息
            throw new BusinessException(ErrorCode.DATA_NOT_FOUND);
        }

        // 4. 构建发送给外部报告服务的请求体
        // 新的契约是发送一个包含ID列表的JSON
        Map<String, Object> requestBodyMap = new HashMap<>();
        requestBodyMap.put("ids", alarmIdsToExport);
        // 如果报告服务还需要其他顶级参数，也可以在这里添加
        // requestBodyMap.put("reportTitle", "自定义报告标题");

        String requestBodyJson = JSONObject.toJSONString(requestBodyMap);

        // 5. 调用外部报告服务
        try {
            log.info("正在调用外部报告服务，URL: {}, 告警数量: {}", alarmReportExprotUrl, alarmIdsToExport.size());
            String result = HttpRequest.post(alarmReportExprotUrl)
                    .body(requestBodyJson) // 发送新的JSON请求体
                    .timeout(60000)
                    .execute()
                    .body();

            log.info("生成告警检测报告PDF成功，返回结果：{}", result);
            JSONObject resultJson = JSONObject.parseObject(result);
            String code = resultJson.getString("code");
            String msg = resultJson.getString("ERROR_TEST_DATA"); // 假设错误信息字段不变
            String filePath = resultJson.getString("data"); // 假设文件路径字段不变

            if (!"200".equals(code)) {
                throw new RuntimeException("报告服务返回错误: " + msg);
            }
            return filePath;

        } catch (Exception e) {
            log.error("调用外部报告服务生成报告时发生异常：{}", e.getMessage(), e);
            throw new BusinessException(ErrorCode.FILE_OPERATION_FAILED);
        }
    }

    /**
     * 校验排序字段，防止SQL注入。
     * @param orderField 前端传入的排序字段名。
     */
    private void validateOrderField(String orderField) {
        if (StringUtils.isEmpty(orderField)) {
            return; // 如果为空，则使用默认排序，是安全的
        }
        // 定义一个允许排序的字段白名单
        List<String> allowedFields = List.of("time", "risk_level", "alarm_name");
        if (!allowedFields.contains(orderField)) {
            log.error("检测到非法的排序字段，可能存在SQL注入风险: {}", orderField);
            throw new BusinessException(ErrorCode.PARAM_VALIDATION_FAILED);
        }
    }

    @Override
    public ApiResponse prepareAlarmSessionPcap(Integer userId, List<String> alarmSessionList, String alarmType, Long alarmTime) {
        log.info("通过告警关联会话ID，生成PCAP文件下载任务 (Doris)，sessionList={}, alarmType={}", alarmSessionList, alarmType);

        // 1. 从Doris数据库中查询会话元数据
        // 用一次数据库查询替代了原有的ES查询
        List<Map<String, Object>> sessionMetadataList = alarmMapper.findSessionMetadataBySessionIds(alarmSessionList);

        // 2. 检查是否找到了数据
        if (sessionMetadataList == null || sessionMetadataList.isEmpty()) {
            log.error("未找到对应的会话元数据，sessionList={}", alarmSessionList);
            return ApiResponse.success("对应的会话元数据正在准备中，请稍后");
        }

        // 3. 遍历查询到的元数据，组装成PCAP下载任务所需的格式
        List<DownloadPcapCondition.Session> pcapSessionList = new ArrayList<>();
        Set<Integer> taskIds = new HashSet<>();
        for (Map<String, Object> metadataMap : sessionMetadataList) {
            DownloadPcapCondition.Session session = new DownloadPcapCondition.Session();

            // 从Map中获取数据，逻辑比之前更清晰
            // batch_id 是从数据库列中直接获取的
            session.setBatchId(metadataMap.get("batch_id").toString());
            session.setStartTime(metadataMap.get("StartTime") == null ? null : Long.valueOf(metadataMap.get("StartTime").toString()));
            session.setEndTime(metadataMap.get("EndTime") == null ? null : Long.valueOf(metadataMap.get("EndTime").toString()));
            session.setSessionId((String) metadataMap.get("SessionId"));
            session.setFirstProto((Integer) metadataMap.get("FirstProto"));

            // task_id 也是从数据库列中直接获取的，不再需要解析字符串
            Integer taskId = (Integer) metadataMap.get("task_id");
            taskIds.add(taskId);
            session.setTaskId(taskId);
            session.setThreadId((Integer) metadataMap.get("ThreadId"));

            pcapSessionList.add(session);
        }

        // 4. 组装 show_query 展示数据内容 (这部分逻辑与原先相同)
        Map<String, Map<String, Object>> showQuery = new HashMap<>();
        showQuery.put("and", new HashMap<>() {{
            put("SessionId", new JSONArray(Collections.singletonList(alarmSessionList)));
        }});
        showQuery.put("not", new HashMap<>());

        // 5. 创建并保存下载任务
        try {
            DownloadTask downloadTask = new DownloadTask();
            downloadTask.setCreatedTime(System.currentTimeMillis() / 1000);
            downloadTask.setUserId(userId);

            // **重要改进**: 不再存储与技术栈强绑定的ES查询语句。
            // 存储一个通用的JSON来描述查询条件，更健壮、更利于未来维护。
            String queryStr = JSONObject.toJSONString(Map.of("sessionIds", alarmSessionList));
            downloadTask.setQuery(queryStr);

            downloadTask.setType(0); // 假设0代表PCAP下载
            downloadTask.setSessionId(JSONArray.toJSONString(pcapSessionList));
            downloadTask.setTaskId(taskIds.toString());
            downloadTask.setState(0); // 任务状态：待处理
            downloadTask.setStatus(1); // 记录状态：有效
            downloadTask.setShowQuery(JSONObject.toJSONString(showQuery));

            // 执行数据库插入操作
            //downloadTaskDao.insert(downloadTask); //TODO 待完善

            Map<String, Object> result = new HashMap<>();
            result.put("id", downloadTask.getId());
            return ApiResponse.success(result);

        } catch (Exception e) {
            log.error("生成告警关联会话PCAP下载任务失败，异常：", e);
            throw new BusinessException(ErrorCode.FILE_OPERATION_FAILED);
        }
    }


    /*private BoolQueryBuilder getCommonQueryBuilder(AlarmCommonCondition commonCondition) {
        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
        Long left = commonCondition.getLeft();
        Long right = commonCondition.getRight();
        RangeQueryBuilder timeRangeQuery = QueryBuilders.rangeQuery("time");
        Boolean isTime = false;
        if (left != null && left > 0) {
            //gt  大于    这里只用流量的开始时间~
            timeRangeQuery.gte(left);
            isTime = true;
        }
        if (right != null && right > 0) {
            timeRangeQuery.lte(right);
            isTime = true;
        }
        if (isTime) {
            boolQueryBuilder.must(timeRangeQuery);
        }

        //告警名称
        List<Integer> alarmIds = commonCondition.getAlarmIds();
        if (alarmIds != null && alarmIds.size() > 0) {
            boolQueryBuilder.must(QueryBuilders.termsQuery("alarm_knowledge_id", alarmIds));
        }
        //taskId
        List<Integer> taskIds = commonCondition.getTaskIds();
        boolQueryBuilder.must(QueryBuilders.termsQuery("task_id", taskIds));

        String targetName = commonCondition.getTargetName();
        if (StringUtils.isNotEmpty(targetName)) {
            boolQueryBuilder.must(QueryBuilders.termsQuery("targets.name.keyword", targetName));
        }

        //受害方 victim 下面的字段都查
        String victims = commonCondition.getVictim();
        if (StringUtils.isNotEmpty(victims)) {
            BoolQueryBuilder shouldBuild = QueryBuilders.boolQuery();
            shouldBuild.should(QueryBuilders.termsQuery("victim.ip", victims));
            boolQueryBuilder.must(shouldBuild);
        }
        //攻击方:ip
        String attackerIp = commonCondition.getAttackerIp();
        if (StringUtils.isNotEmpty(attackerIp)) {
            boolQueryBuilder.must(QueryBuilders.termsQuery("attacker.ip", attackerIp));
        }
        //威胁等级
        List<String> attackLevels = commonCondition.getAttackLevels();
        if (attackLevels != null && attackLevels.size() > 0) {
            BoolQueryBuilder shouldBool = new BoolQueryBuilder();
            for (String attackLevel : attackLevels) {
                if (StringUtils.isNotEmpty(attackLevel)) {

                    String[] split = attackLevel.split("-");
                    shouldBool.should(QueryBuilders.rangeQuery("attack_level").gte(split[0]).lte(split[1]));
                }
            }
            boolQueryBuilder.must(shouldBool);
        }
        //处理状态
        List<Integer> alarmStatusList = commonCondition.getAlarmStatusList();
        if (alarmStatusList != null && alarmStatusList.size() > 0) {
            boolQueryBuilder.must(QueryBuilders.termsQuery("alarm_status", alarmStatusList));
        }

        return boolQueryBuilder;
    }*/


    private BusinessException checkParam(AlarmCommonCondition commonCondition) {
        List<Integer> taskIds = commonCondition.getTaskIds();
        if (taskIds == null || taskIds.size() < 1) {
            throw new BusinessException(ErrorCode.PARAM_VALIDATION_FAILED);
        }
        List<String> attackLevels = commonCondition.getAttackLevels();
        if (attackLevels != null && attackLevels.size() > 0) {
            for (String attackLevel : attackLevels) {
                if (StringUtils.isNotEmpty(attackLevel)) {
                    String[] split = attackLevel.split("-");
                    try {
                        Integer left = Integer.valueOf(split[0]);
                        Integer right = Integer.valueOf(split[1]);
                        if (left > right || left < 61 || right > 100) {
                            throw new BusinessException(ErrorCode.PARAM_VALIDATION_FAILED);
                        }
                    } catch (Exception e) {
                        throw new BusinessException(ErrorCode.PARAM_VALIDATION_FAILED);
                    }
                }
            }
        }

        return null;
    }

    private List<CommonRangeDto> getLevelRange() {
        List<CommonRangeDto> list = new ArrayList<>();

        CommonRangeDto low = new CommonRangeDto();
        low.setLeft(60);
        low.setRight(80);
        low.setKey("low");
        list.add(low);

        CommonRangeDto middle = new CommonRangeDto();
        middle.setLeft(81);
        middle.setRight(90);
        middle.setKey("middle");
        list.add(middle);

        CommonRangeDto high = new CommonRangeDto();
        high.setLeft(91);
        high.setRight(100);
        high.setKey("high");
        list.add(high);
        return list;
    }
}
